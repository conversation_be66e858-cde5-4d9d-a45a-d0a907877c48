/**
 * 飞书表格结构查看工具
 *
 * 模块名称：飞书表格结构查看工具
 * 模块描述：查看飞书多维表的字段结构和现有记录
 * 模块职责：飞书API调用、表格结构获取、字段信息展示
 * 修改时间: 2025-07-26 15:00
 */

const https = require("https");

//------------------
// 配置区域
//------------------

// 飞书API配置
const FEISHU_CONFIG = {
  APP_ID: "cli_a73da8fdc6fe900d",
  APP_SECRET: "CM0Vl3kDoKUHRim3JuJznXT1rqTBbrQa",
  BASE_URL: "https://open.feishu.cn/open-apis",
  
  // 多维表信息
  BASE_ID: "E1Q3bDq3harjR1s8qr3cyKz6n1b",
  TABLE_ID: "tblwJFuLDpV62Z9p",
  
  // API超时设置
  API_TIMEOUT: 30000,
};

//------------------
// 工具函数
//------------------

/**
 * 日志输出函数
 * @param {string} level - 日志级别
 * @param {string} message - 日志消息
 */
function log(level, message) {
  const timestamp = new Date().toISOString();
  console.log(`${timestamp} [${level}] ${message}`);
}

/**
 * 通用HTTP请求函数
 * @param {string} url - 请求URL
 * @param {Object} options - 请求选项
 * @returns {Promise<Object>} 响应数据
 */
async function makeHttpRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === "https:";
    const httpModule = isHttps ? require("https") : require("http");
    
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || "GET",
      headers: options.headers || {},
      timeout: options.timeout || 30000,
    };

    const req = httpModule.request(requestOptions, (res) => {
      let data = "";
      
      res.on("data", (chunk) => {
        data += chunk;
      });
      
      res.on("end", () => {
        try {
          const jsonData = JSON.parse(data);
          resolve(jsonData);
        } catch (error) {
          reject(new Error(`JSON解析失败: ${error.message}`));
        }
      });
    });

    req.on("error", (error) => {
      reject(new Error(`请求失败: ${error.message}`));
    });

    req.on("timeout", () => {
      req.destroy();
      reject(new Error("请求超时"));
    });

    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

//------------------
// 飞书API函数
//------------------

/**
 * 获取飞书访问令牌
 * @returns {Promise<string|null>} 访问令牌
 */
async function getFeishuAccessToken() {
  try {
    const url = `${FEISHU_CONFIG.BASE_URL}/auth/v3/tenant_access_token/internal`;
    const data = JSON.stringify({
      app_id: FEISHU_CONFIG.APP_ID,
      app_secret: FEISHU_CONFIG.APP_SECRET,
    });

    const response = await makeHttpRequest(url, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: data,
      timeout: FEISHU_CONFIG.API_TIMEOUT,
    });

    if (response && response.code === 0) {
      log("DEBUG", "飞书token获取成功");
      return response.tenant_access_token;
    }

    throw new Error(`获取token失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `飞书token获取失败: ${error.message}`);
    return null;
  }
}

/**
 * 获取表格字段信息
 * @param {string} baseId - 多维表BaseID
 * @param {string} tableId - 表格ID
 * @returns {Promise<Array|null>} 字段列表
 */
async function getTableFields(baseId, tableId) {
  try {
    const token = await getFeishuAccessToken();
    if (!token) {
      throw new Error("获取飞书token失败");
    }

    const url = `${FEISHU_CONFIG.BASE_URL}/bitable/v1/apps/${baseId}/tables/${tableId}/fields`;
    
    const response = await makeHttpRequest(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      timeout: FEISHU_CONFIG.API_TIMEOUT,
    });

    if (response && response.code === 0) {
      log("INFO", `表格字段获取成功: ${response.data.items.length}个字段`);
      return response.data.items;
    }

    throw new Error(`获取字段失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `表格字段获取失败: ${error.message}`);
    return null;
  }
}

/**
 * 获取表格记录（前几条）
 * @param {string} baseId - 多维表BaseID
 * @param {string} tableId - 表格ID
 * @returns {Promise<Array|null>} 记录列表
 */
async function getTableRecords(baseId, tableId, pageSize = 3) {
  try {
    const token = await getFeishuAccessToken();
    if (!token) {
      throw new Error("获取飞书token失败");
    }

    const url = `${FEISHU_CONFIG.BASE_URL}/bitable/v1/apps/${baseId}/tables/${tableId}/records?page_size=${pageSize}`;
    
    const response = await makeHttpRequest(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      timeout: FEISHU_CONFIG.API_TIMEOUT,
    });

    if (response && response.code === 0) {
      log("INFO", `表格记录获取成功: ${response.data.items.length}条记录`);
      return response.data.items;
    }

    throw new Error(`获取记录失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `表格记录获取失败: ${error.message}`);
    return null;
  }
}

//------------------
// 主要功能函数
//------------------

/**
 * 执行表格结构查看
 * @returns {Promise<Object>} 查看结果
 */
async function runTableInspection() {
  log("INFO", "=== 开始飞书表格结构查看 ===");
  
  try {
    // 1. 获取表格字段信息
    log("INFO", "第1步: 获取表格字段信息");
    const fields = await getTableFields(FEISHU_CONFIG.BASE_ID, FEISHU_CONFIG.TABLE_ID);
    
    if (!fields) {
      throw new Error("获取字段信息失败");
    }

    log("INFO", "=== 表格字段信息 ===");
    fields.forEach((field, index) => {
      log("INFO", `字段${index + 1}: ${field.field_name} (${field.type}) - ID: ${field.field_id}`);
    });

    // 2. 获取现有记录示例
    log("INFO", "第2步: 获取现有记录示例");
    const records = await getTableRecords(FEISHU_CONFIG.BASE_ID, FEISHU_CONFIG.TABLE_ID);
    
    if (records && records.length > 0) {
      log("INFO", "=== 现有记录示例 ===");
      records.forEach((record, index) => {
        log("INFO", `记录${index + 1} (ID: ${record.record_id}):`);
        Object.entries(record.fields).forEach(([key, value]) => {
          log("INFO", `  ${key}: ${value}`);
        });
        log("INFO", "---");
      });
    }

    // 3. 生成字段映射建议
    const fieldMapping = {};
    fields.forEach(field => {
      fieldMapping[field.field_name] = field.field_id;
    });

    const result = {
      status: "success",
      message: "表格结构查看成功",
      data: {
        base_id: FEISHU_CONFIG.BASE_ID,
        table_id: FEISHU_CONFIG.TABLE_ID,
        fields: fields,
        field_mapping: fieldMapping,
        sample_records: records,
        field_count: fields.length,
        record_count: records ? records.length : 0,
      },
    };

    log("INFO", "=== 飞书表格结构查看完成 ===");
    log("INFO", `字段数量: ${fields.length}`);
    log("INFO", `示例记录: ${records ? records.length : 0}条`);

    return result;
  } catch (error) {
    log("ERROR", `=== 飞书表格结构查看失败 ===`);
    log("ERROR", `错误信息: ${error.message}`);

    return {
      status: "error",
      message: error.message,
    };
  }
}

//------------------
// 执行入口
//------------------

if (require.main === module) {
  runTableInspection()
    .then((result) => {
      console.log("=== 最终结果 ===");
      console.log(JSON.stringify(result, null, 2));
      process.exit(result.status === "success" ? 0 : 1);
    })
    .catch((error) => {
      console.error("执行错误:", error);
      process.exit(1);
    });
}

module.exports = {
  runTableInspection,
  getTableFields,
  getTableRecords,
  getFeishuAccessToken,
  FEISHU_CONFIG,
};
