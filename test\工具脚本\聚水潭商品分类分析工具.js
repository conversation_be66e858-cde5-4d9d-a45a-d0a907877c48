/**
 * 聚水潭商品分类分析工具
 *
 * 功能：查询现有商品的分类信息，分析分类字段的实际填写规范
 * 目的：为商品创建时的分类参数提供正确的参考格式
 */

const crypto = require("crypto");

//===================================================================================
// 📋 配置参数
//===================================================================================

const JUSHUITAN_CONFIG = {
  APP_KEY: "13a2701994bd4230a1ed9a12302ba30a",
  APP_SECRET: "f6e5dd9d168d4817973cb42f121218a0",
  ACCESS_TOKEN: "9a9d33072cc8450b916b7c8dd830d22c",
  BASE_URL: "https://openapi.jushuitan.com",
  API_TIMEOUT: 30000,
  RETRY_TIMES: 3,
  RETRY_DELAY: 1000,
};

//===================================================================================
// 🛠️ 工具函数
//===================================================================================

function log(level, ...messages) {
  const timestamp = new Date().toISOString();
  const logMessage = `${timestamp} [${level.toUpperCase()}] ${messages.join(
    " "
  )}`;

  switch (level.toUpperCase()) {
    case "INFO":
      console.info(logMessage);
      break;
    case "ERROR":
      console.error(logMessage);
      break;
    case "WARN":
      console.warn(logMessage);
      break;
    case "DEBUG":
      console.log(logMessage);
      break;
    default:
      console.log(logMessage);
  }
}

function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

async function calculateMD5(str) {
  return crypto.createHash("md5").update(str).digest("hex");
}

//===================================================================================
// 🔧 API调用函数
//===================================================================================

function buildJushuitanParams(bizParams) {
  const params = {
    access_token: JUSHUITAN_CONFIG.ACCESS_TOKEN,
    app_key: JUSHUITAN_CONFIG.APP_KEY,
    charset: "utf-8",
    timestamp: Math.floor(Date.now() / 1000),
    version: "2",
    biz: JSON.stringify(bizParams),
  };

  // 按ASCII码排序
  const sortedParams = {};
  const keys = Object.keys(params).sort();
  keys.forEach((key) => {
    sortedParams[key] = params[key];
  });

  return sortedParams;
}

async function generateJushuitanSignature(params) {
  try {
    const filteredKeys = Object.keys(params)
      .filter((key) => key !== "sign")
      .sort();

    const signParts = filteredKeys.map((key) => key + params[key]);
    const signString = JUSHUITAN_CONFIG.APP_SECRET + signParts.join("");

    return await calculateMD5(signString);
  } catch (error) {
    log("ERROR", `签名生成失败: ${error.message}`);
    return null;
  }
}

async function callJushuitanAPI(apiPath, bizParams, apiName) {
  const maxRetries = JUSHUITAN_CONFIG.RETRY_TIMES;
  const apiUrl = `${JUSHUITAN_CONFIG.BASE_URL}${apiPath}`;

  try {
    log("INFO", `调用聚水潭API: ${apiName}`);

    // 构建参数和签名
    const params = buildJushuitanParams(bizParams);
    const sign = await generateJushuitanSignature(params);
    if (!sign) {
      throw new Error("签名生成失败");
    }
    params.sign = sign;

    // 构建请求体
    const formData = new URLSearchParams();
    Object.keys(params).forEach((key) => {
      formData.append(key, params[key]);
    });

    // 重试机制
    for (let retryCount = 0; retryCount < maxRetries; retryCount++) {
      try {
        log("INFO", `正在请求 ${apiName} (第${retryCount + 1}次尝试)...`);

        const controller = new AbortController();
        const timeoutId = setTimeout(
          () => controller.abort(),
          JUSHUITAN_CONFIG.API_TIMEOUT
        );

        const response = await fetch(apiUrl, {
          method: "POST",
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
          body: formData,
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          const responseText = await response.text();
          const result = JSON.parse(responseText);

          if (result.code === 0) {
            log("INFO", `${apiName} 请求成功`);
            return result.data;
          } else if (result.code === 199 || result.code === 200) {
            log(
              "WARN",
              `${apiName} 频率限制: ${result.msg}，等待重试...（第${
                retryCount + 1
              }次）`
            );
            await delay(JUSHUITAN_CONFIG.RETRY_DELAY * (retryCount + 1));
            continue;
          } else {
            log(
              "WARN",
              `${apiName} 错误响应: ${JSON.stringify(result)}，等待重试...（第${
                retryCount + 1
              }次）`
            );
            await delay(JUSHUITAN_CONFIG.RETRY_DELAY);
            continue;
          }
        } else {
          throw new Error(`HTTP请求失败: ${response.status}`);
        }
      } catch (httpError) {
        log(
          "WARN",
          `${apiName} 请求异常: ${httpError.message}，等待重试...（第${
            retryCount + 1
          }次）`
        );
        if (retryCount === maxRetries - 1) {
          throw httpError;
        }
        await delay(JUSHUITAN_CONFIG.RETRY_DELAY * (retryCount + 1));
        continue;
      }
    }

    log("ERROR", `${apiName} 重试 ${maxRetries} 次失败`);
    return null;
  } catch (error) {
    log("ERROR", `聚水潭API调用失败 [${apiName}]: ${error.message}`);
    return null;
  }
}

//===================================================================================
// 📊 商品分类分析功能
//===================================================================================

/**
 * 查询商品列表并分析分类信息
 */
async function analyzeProductCategories() {
  try {
    log("INFO", "开始分析聚水潭商品分类信息...");

    const allProducts = [];
    let pageIndex = 1;
    const pageSize = 50; // 每页查询50个商品
    let hasMore = true;
    let totalProducts = 0;

    while (hasMore && pageIndex <= 10) {
      // 限制最多查询10页，500个商品
      try {
        log("INFO", `正在查询第 ${pageIndex} 页商品数据...`);

        const queryParams = {
          page_index: pageIndex,
          page_size: pageSize,
          // 不指定特定商品，查询所有商品
        };

        const result = await callJushuitanAPI(
          "/open/sku/query",
          queryParams,
          `商品查询-第${pageIndex}页`
        );

        if (!result) {
          log("WARN", `第 ${pageIndex} 页查询失败，跳过`);
          break;
        }

        const products = result.datas || [];
        totalProducts += products.length;

        // 收集商品分类信息
        products.forEach((product) => {
          allProducts.push({
            sku_id: product.sku_id,
            i_id: product.i_id,
            name: product.name,
            c_name: product.c_name, // 重点关注分类字段
            vc_name: product.vc_name, // 可能的分类名称字段
            brand: product.brand,
            enabled: product.enabled,
            modified: product.modified,
          });
        });

        log(
          "INFO",
          `第 ${pageIndex} 页: ${products.length} 个商品，累计: ${totalProducts} 个`
        );

        // 检查是否还有下一页
        hasMore = result.has_next;
        pageIndex++;

        // 添加延迟避免API频率限制
        if (hasMore) {
          await delay(500);
        }
      } catch (error) {
        log("ERROR", `查询第 ${pageIndex} 页时发生错误: ${error.message}`);
        break;
      }
    }

    if (totalProducts === 0) {
      log("ERROR", "未获取到任何商品数据");
      return null;
    }

    log("INFO", `商品查询完成! 共获取 ${totalProducts} 个商品`);

    // 分析分类信息
    return analyzeCategories(allProducts);
  } catch (error) {
    log("ERROR", `商品分类分析失败: ${error.message}`);
    return null;
  }
}

/**
 * 分析商品分类数据
 */
function analyzeCategories(products) {
  log("INFO", "开始分析分类数据...");

  const categoryAnalysis = {
    total_products: products.length,
    category_stats: {
      with_c_name: 0,
      with_vc_name: 0,
      without_category: 0,
    },
    c_name_values: new Set(),
    vc_name_values: new Set(),
    category_examples: [],
    unique_c_names: [],
    unique_vc_names: [],
    recommendations: [],
  };

  // 统计分类字段使用情况
  products.forEach((product) => {
    // 统计 c_name 字段
    if (product.c_name && product.c_name.trim()) {
      categoryAnalysis.category_stats.with_c_name++;
      categoryAnalysis.c_name_values.add(product.c_name.trim());
    }

    // 统计 vc_name 字段
    if (product.vc_name && product.vc_name.trim()) {
      categoryAnalysis.category_stats.with_vc_name++;
      categoryAnalysis.vc_name_values.add(product.vc_name.trim());
    }

    // 无分类商品
    if (
      (!product.c_name || !product.c_name.trim()) &&
      (!product.vc_name || !product.vc_name.trim())
    ) {
      categoryAnalysis.category_stats.without_category++;
    }

    // 收集前20个有分类的商品作为示例
    if (categoryAnalysis.category_examples.length < 20) {
      if (
        (product.c_name && product.c_name.trim()) ||
        (product.vc_name && product.vc_name.trim())
      ) {
        categoryAnalysis.category_examples.push({
          sku_id: product.sku_id,
          name: product.name,
          c_name: product.c_name || "",
          vc_name: product.vc_name || "",
          brand: product.brand || "",
        });
      }
    }
  });

  // 转换Set为数组并排序
  categoryAnalysis.unique_c_names = Array.from(
    categoryAnalysis.c_name_values
  ).sort();
  categoryAnalysis.unique_vc_names = Array.from(
    categoryAnalysis.vc_name_values
  ).sort();

  // 生成建议
  generateRecommendations(categoryAnalysis);

  return categoryAnalysis;
}

/**
 * 生成分类使用建议
 */
function generateRecommendations(analysis) {
  const recommendations = [];

  // 分析主要使用的分类字段
  if (
    analysis.category_stats.with_c_name > analysis.category_stats.with_vc_name
  ) {
    recommendations.push({
      type: "PRIMARY_FIELD",
      message: `建议使用 c_name 字段作为主要分类字段 (${analysis.category_stats.with_c_name}个商品使用)`,
      field: "c_name",
      usage_count: analysis.category_stats.with_c_name,
    });
  } else if (analysis.category_stats.with_vc_name > 0) {
    recommendations.push({
      type: "PRIMARY_FIELD",
      message: `建议使用 vc_name 字段作为主要分类字段 (${analysis.category_stats.with_vc_name}个商品使用)`,
      field: "vc_name",
      usage_count: analysis.category_stats.with_vc_name,
    });
  }

  // 分析最常用的分类值
  if (analysis.unique_c_names.length > 0) {
    const topCategories = analysis.unique_c_names.slice(0, 10);
    recommendations.push({
      type: "COMMON_VALUES",
      message: `c_name 字段的常用值: ${topCategories.join(", ")}`,
      field: "c_name",
      values: topCategories,
    });
  }

  if (analysis.unique_vc_names.length > 0) {
    const topCategories = analysis.unique_vc_names.slice(0, 10);
    recommendations.push({
      type: "COMMON_VALUES",
      message: `vc_name 字段的常用值: ${topCategories.join(", ")}`,
      field: "vc_name",
      values: topCategories,
    });
  }

  // 空分类建议
  if (analysis.category_stats.without_category > 0) {
    recommendations.push({
      type: "EMPTY_CATEGORY",
      message: `发现 ${analysis.category_stats.without_category} 个商品没有分类，建议可以使用空分类创建商品`,
      empty_count: analysis.category_stats.without_category,
    });
  }

  analysis.recommendations = recommendations;
}

/**
 * 保存分析结果到文件
 */
async function saveAnalysisResult(analysis) {
  try {
    const fs = require("fs");
    const path = require("path");

    const outputDir = path.join(__dirname, "../配置数据");
    const outputFile = path.join(outputDir, "聚水潭商品分类分析结果.json");

    // 确保目录存在
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // 准备保存的数据
    const saveData = {
      analysis_time: new Date().toISOString(),
      ...analysis,
      // 转换Set为数组以便JSON序列化
      c_name_values: Array.from(analysis.c_name_values),
      vc_name_values: Array.from(analysis.vc_name_values),
    };

    // 删除原始Set对象
    delete saveData.c_name_values;
    delete saveData.vc_name_values;

    fs.writeFileSync(outputFile, JSON.stringify(saveData, null, 2));
    log("INFO", `分析结果已保存到: ${outputFile}`);

    return outputFile;
  } catch (error) {
    log("ERROR", `保存分析结果失败: ${error.message}`);
    return null;
  }
}

/**
 * 输出分析报告
 */
function printAnalysisReport(analysis) {
  console.log("\n" + "=".repeat(80));
  console.log("📊 聚水潭商品分类分析报告");
  console.log("=".repeat(80));

  console.log(`\n📈 统计概览:`);
  console.log(`  总商品数: ${analysis.total_products}`);
  console.log(
    `  有 c_name 分类: ${analysis.category_stats.with_c_name} (${(
      (analysis.category_stats.with_c_name / analysis.total_products) *
      100
    ).toFixed(1)}%)`
  );
  console.log(
    `  有 vc_name 分类: ${analysis.category_stats.with_vc_name} (${(
      (analysis.category_stats.with_vc_name / analysis.total_products) *
      100
    ).toFixed(1)}%)`
  );
  console.log(
    `  无分类商品: ${analysis.category_stats.without_category} (${(
      (analysis.category_stats.without_category / analysis.total_products) *
      100
    ).toFixed(1)}%)`
  );

  console.log(`\n🏷️ c_name 分类值 (${analysis.unique_c_names.length}个):`);
  if (analysis.unique_c_names.length > 0) {
    analysis.unique_c_names.slice(0, 20).forEach((name, index) => {
      console.log(`  ${index + 1}. "${name}"`);
    });
    if (analysis.unique_c_names.length > 20) {
      console.log(`  ... 还有 ${analysis.unique_c_names.length - 20} 个分类`);
    }
  }

  console.log(`\n🏷️ vc_name 分类值 (${analysis.unique_vc_names.length}个):`);
  if (analysis.unique_vc_names.length > 0) {
    analysis.unique_vc_names.slice(0, 20).forEach((name, index) => {
      console.log(`  ${index + 1}. "${name}"`);
    });
    if (analysis.unique_vc_names.length > 20) {
      console.log(`  ... 还有 ${analysis.unique_vc_names.length - 20} 个分类`);
    }
  }

  console.log(`\n📋 商品示例 (前10个):`);
  analysis.category_examples.slice(0, 10).forEach((example, index) => {
    console.log(`  ${index + 1}. ${example.sku_id}`);
    console.log(`     名称: ${example.name}`);
    console.log(`     c_name: "${example.c_name}"`);
    console.log(`     vc_name: "${example.vc_name}"`);
    console.log(`     品牌: "${example.brand}"`);
    console.log("");
  });

  console.log(`\n💡 使用建议:`);
  analysis.recommendations.forEach((rec, index) => {
    console.log(`  ${index + 1}. ${rec.message}`);
  });

  console.log("\n" + "=".repeat(80));
}

//===================================================================================
// 🎯 主函数
//===================================================================================

async function main() {
  try {
    log("INFO", "开始聚水潭商品分类分析...");

    // 分析商品分类
    const analysis = await analyzeProductCategories();

    if (!analysis) {
      log("ERROR", "分类分析失败");
      process.exit(1);
    }

    // 输出报告
    printAnalysisReport(analysis);

    // 保存结果
    const outputFile = await saveAnalysisResult(analysis);

    log("INFO", "分类分析完成!");
    if (outputFile) {
      log("INFO", `详细结果已保存到: ${outputFile}`);
    }

    return analysis;
  } catch (error) {
    log("ERROR", `执行失败: ${error.message}`);
    process.exit(1);
  }
}

// 如果直接执行此文件
if (require.main === module) {
  console.log("开始执行商品分类分析...");
  main()
    .then(() => {
      console.log("分析完成，程序正常退出");
      process.exit(0);
    })
    .catch((error) => {
      console.error("执行错误:", error);
      console.error("错误堆栈:", error.stack);
      process.exit(1);
    });
}

module.exports = {
  main,
  analyzeProductCategories,
  analyzeCategories,
  saveAnalysisResult,
  callJushuitanAPI,
};
