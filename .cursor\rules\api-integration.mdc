---
description: 需要通过聚水潭API或飞书API增删改查时查看
alwaysApply: false
---
# 🔗 API集成开发规范

## 🏪 聚水潭API集成标准

基于 [聚水潭API集成细节说明.md](mdc:docs/聚水潭API集成细节说明.md) 的规范要求：

### 1. API调用标准流程
```javascript
// 1. 参数构建
const bizParams = { /* 业务参数 */ };
const params = buildJushuitanParams(bizParams);

// 2. 签名生成  
const sign = generateJushuitanSignature(params);
params.sign = sign;

// 3. 请求发送 (带重试机制)
const result = callJushuitanAPIWithRetry(apiUrl, bizParams, apiName);

// 4. 结果验证
if (result) {
    // 成功处理逻辑
    return result;
} else {
    // 失败处理逻辑
    logAndBuffer("ERROR", `${apiName} 最终失败`);
}
```

### 2. 聚水潭签名算法规范

基于官方文档 [docs/jushuitan_api/聚水潭开放平台-签名规则.md](mdc:docs/jushuitan_api/聚水潭开放平台-签名规则.md) 的标准实现：

#### 2.1 参数构建规范
```javascript
function buildJushuitanParams(bizParams) {
    const params = {
        access_token: ACCESS_TOKEN,
        app_key: APP_KEY,
        charset: "utf-8",
        timestamp: Math.floor(Date.now() / 1000),
        version: "2",
        biz: JSON.stringify(bizParams, null, 0) // 无格式化，紧凑JSON
    };
    
    // 按ASCII码排序 (重要!)
    return Object.keys(params)
        .sort()
        .reduce((result, key) => {
            result[key] = params[key];
            return result;
        }, {});
}
```

#### 2.2 签名生成算法
```javascript
function generateJushuitanSignature(params) {
    // 1. 排除sign参数，按key的ASCII码排序
    const filteredParams = Object.keys(params)
        .filter(key => key !== "sign")
        .sort()
        .map(key => key + params[key])
        .join("");
    
    // 2. app_secret + 排序后的参数字符串
    const signString = APP_SECRET + filteredParams;
    
    // 3. MD5加密并转为32位小写
    return calculateMD5(signString).toLowerCase();
}
```

#### 2.3 签名关键规则
- **字典排序**: 必须按key的ASCII码严格排序
- **biz参数**: JSON字符串作为整体，不拆分内部字段  
- **MD5编码**: 使用UTF-8编码计算MD5
- **app_secret位置**: 必须在最前面
- **大小写**: 最终签名必须是32位小写

### 3. 重试机制和错误处理规范

基于成熟的API集成实践：

#### 3.1 重试配置
```javascript
const RETRY_CONFIG = {
    MAX_RETRIES: 5,                    // 最大重试次数
    API_SUCCESS_DELAY: 600,            // 成功后延迟(毫秒)，控制频率
    RATE_LIMIT_WAIT_TIME: 45000,       // 频率限制等待时间(毫秒)
    OTHER_ERROR_WAIT_TIME: 15000       // 其他错误等待时间(毫秒)
};
```

#### 3.2 标准错误码处理
```javascript
function handleJushuitanResponse(result, apiName, retryCount) {
    if (result.code === 0) {
        // 成功
        logAndBuffer("INFO", `${apiName} 请求成功`);
        return { success: true, data: result.data, shouldRetry: false };
    } else if (result.code === 199 || result.code === 200) {
        // 频率限制
        logAndBuffer("WARN", `${apiName} 频率限制: ${result.msg}，等待重试...（第${retryCount + 1}次）`);
        return { success: false, data: null, shouldRetry: true, waitTime: RETRY_CONFIG.RATE_LIMIT_WAIT_TIME };
    } else {
        // 其他API错误码
        logAndBuffer("WARN", `${apiName} 错误响应: ${JSON.stringify(result)}，等待重试...（第${retryCount + 1}次）`);
        return { success: false, data: null, shouldRetry: true, waitTime: RETRY_CONFIG.OTHER_ERROR_WAIT_TIME };
    }
}
```

#### 3.3 完整重试逻辑
```javascript
async function callJushuitanAPIWithRetry(apiUrl, bizParams, apiName) {
    let retryCount = 0;
    
    while (retryCount < RETRY_CONFIG.MAX_RETRIES) {
        try {
            logAndBuffer("INFO", `正在请求 ${apiName} (第${retryCount + 1}次尝试)...`);
            
            const params = buildJushuitanParams(bizParams);
            params.sign = generateJushuitanSignature(params);
            
            const response = await HTTP.post(apiUrl, buildFormData(params), {
                headers: { "Content-Type": "application/x-www-form-urlencoded" },
                timeout: 20000
            });
            
            if (response.status === 200) {
                const result = JSON.parse(response.body);
                const handleResult = handleJushuitanResponse(result, apiName, retryCount);
                
                if (handleResult.success) {
                    // 成功后延迟，控制API调用频率
                    await sleep(RETRY_CONFIG.API_SUCCESS_DELAY);
                    return handleResult.data;
                } else if (handleResult.shouldRetry) {
                    await sleep(handleResult.waitTime);
                } else {
                    return null;
                }
            } else {
                throw new Error(`HTTP请求失败: ${response.status}`);
            }
            
        } catch (error) {
            if (error.name === 'TimeoutError') {
                logAndBuffer("WARN", `${apiName} 请求超时，等待重试...（第${retryCount + 1}次）`);
            } else {
                logAndBuffer("WARN", `${apiName} 请求异常: ${error.message}，等待重试...（第${retryCount + 1}次）`);
            }
            await sleep(RETRY_CONFIG.OTHER_ERROR_WAIT_TIME);
        }
        
        retryCount++;
    }
    
    logAndBuffer("ERROR", `${apiName} 重试 ${RETRY_CONFIG.MAX_RETRIES} 次失败，跳过本次请求`);
    return null;
}
```

### 4. API参数规范
- **商品查询**: 使用 `sku_ids` 参数（不是 `sku`）
- **商品创建**: 必须包含 `i_id`、`enabled`(数字1)、`supplier_i_id` 等必填字段
- **供应商创建**: 必须包含 `supplier_code` 字段
- **采购单创建**: 使用正确的API endpoint `/open/jushuitan/purchase/upload`

### 5. API批处理规范

基于成熟的批量处理实践：

#### 5.1 批处理大小限制
```javascript
const BATCH_LIMITS = {
    SKU_QUERY: 20,           // 商品查询最大20个sku_id
    INVENTORY_QUERY: 100,    // 库存查询最大100个sku_id  
    PURCHASE_QUERY: 50,      // 采购单查询最大50条
    PRODUCT_UPLOAD: 200      // 商品上传最大200条
};
```

#### 5.2 分批处理模式
```javascript
function processBatch(items, batchSize, processFunction, apiName) {
    const results = [];
    
    for (let i = 0; i < items.length; i += batchSize) {
        const batch = items.slice(i, i + batchSize);
        const batchNumber = Math.floor(i / batchSize) + 1;
        const totalBatches = Math.ceil(items.length / batchSize);
        
        logAndBuffer("INFO", `处理${apiName}批次${batchNumber}/${totalBatches}，本批${batch.length}条记录`);
        
        const batchResult = processFunction(batch, batchNumber);
        if (batchResult) {
            results.push(...batchResult);
        }
    }
    
    return results;
}
```

#### 5.3 日期分段查询
```javascript
// 用于大时间跨度的查询，按7天分段
function queryByDateRange(startDate, endDate, queryFunction) {
    const results = [];
    const delta = 7 * 24 * 60 * 60 * 1000; // 7天
    let currentStart = new Date(startDate);
    
    while (currentStart < endDate) {
        const currentEnd = new Date(Math.min(currentStart.getTime() + delta, endDate.getTime()));
        
        logAndBuffer("INFO", `查询日期区间: ${currentStart.toISOString().split('T')[0]} ~ ${currentEnd.toISOString().split('T')[0]}`);
        
        const rangeResult = queryFunction(currentStart, currentEnd);
        if (rangeResult) {
            results.push(...rangeResult);
        }
        
        currentStart = new Date(currentEnd.getTime() + 24 * 60 * 60 * 1000); // 下一天
    }
    
    return results;
}
```

## 📱 飞书API集成标准

### 1. 认证和令牌管理

基于官方API文档的标准实现：

#### 1.1 获取tenant_access_token
```javascript
async function getFeishuTenantAccessToken() {
    const response = await HTTP.post("https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal/", {
        headers: { "Content-Type": "application/json; charset=utf-8" },
        body: JSON.stringify({
            app_id: FEISHU_CONFIG.APP_ID,
            app_secret: FEISHU_CONFIG.APP_SECRET
        })
    });
    
    if (response.status === 200) {
        const result = JSON.parse(response.body);
        return result.tenant_access_token;
    }
    
    throw new Error(`获取飞书token失败: ${response.status}`);
}
```

#### 1.2 令牌缓存策略
- **缓存时间**: token有效期内（通常2小时）
- **自动刷新**: 在token过期前预先获取新token
- **错误重试**: token失效时自动重新获取

### 2. 多维表(Bitable)操作规范

#### 2.1 记录更新标准格式
```javascript
const updateRecordData = {
    fields: {
        "处理状态": status,            // 单选字段
        "采购单号": purchase_order_id, // 文本字段
        "处理时间": new Date().toISOString().split('T')[0], // 日期字段
        "错误信息": error_message || "", // 多行文本字段
        "附件": [                      // 附件字段
            { file_token: "boxcnzm3dPEcutYDPplx5iDak4b" }
        ]
    }
};
```

#### 2.2 API调用示例
```javascript
async function updateFeishuRecord(baseId, tableId, recordId, updateData) {
    const token = await getFeishuTenantAccessToken();
    
    const response = await HTTP.patch(
        `https://open.feishu.cn/open-apis/bitable/v1/apps/${baseId}/tables/${tableId}/records/${recordId}`,
        {
            headers: {
                "Authorization": `Bearer ${token}`,
                "Content-Type": "application/json; charset=utf-8"
            },
            body: JSON.stringify(updateData)
        }
    );
    
    return JSON.parse(response.body);
}
```

### 3. Webhook事件处理规范

#### 3.1 URL验证处理
```javascript
// 无加密情况下的URL验证
function handleUrlVerification(requestBody) {
    if (requestBody.type === "url_verification") {
        return {
            challenge: requestBody.challenge
        };
    }
}
```

#### 3.2 事件数据结构验证
基于测试数据 [test_feishu_data.json](mdc:demo_data/test_feishu_data.json) 的格式：
- **必填字段验证**: `内部款式编码`、`颜色`、`采购单价`、`档口`
- **数量字段验证**: 至少一个尺码数量大于0
- **数据类型验证**: 价格必须为数字，数量必须为正整数

#### 3.3 事件处理标准流程
```javascript
function handleFeishuWebhook(event) {
    // 1. 验证事件类型
    if (event.header.event_type === "drive.file.bitable_record_changed_v1") {
        // 2. 提取表格和记录信息
        const { file_token, table_id, record_id } = event.event;
        
        // 3. 获取完整记录数据
        const recordData = getRecordData(file_token, table_id, record_id);
        
        // 4. 业务逻辑处理
        return processOrderData(recordData);
    }
}
```

### 4. 字段类型映射规范

基于飞书API文档的字段类型：

| 飞书字段类型 | JavaScript处理 | 注意事项 |
|-------------|----------------|----------|
| 文本 | string | 直接使用 |
| 多行文本 | string | 处理换行符 |
| 数字 | number | 验证数值格式 |
| 日期 | string | ISO格式：YYYY-MM-DD |
| 单选 | string | 选项值或选项ID |
| 复选框 | boolean | true/false |
| 附件 | Array<{file_token}> | 文件token数组 |
| 双向关联 | Array<{record_id, table_id}> | 关联记录信息 |

### 5. 错误处理和重试

#### 5.1 常见错误码处理
```javascript
function handleFeishuAPIError(response) {
    const result = JSON.parse(response.body);
    
    switch(result.code) {
        case 0:
            return result.data; // 成功
        case 99991663:
            // token失效，重新获取
            return refreshTokenAndRetry();
        case 99991661:
            // 权限不足
            logAndBuffer("ERROR", "飞书API权限不足");
            return null;
        default:
            logAndBuffer("ERROR", `飞书API错误: ${result.msg}`);
            return null;
    }
}
```

#### 5.2 API限频控制
- **调用频率**: 每秒不超过10次
- **批量操作**: 优先使用批量API接口
- **错误重试**: 遇到限频错误等待后重试

## 🔄 业务数据映射规范

### SKU生成规则
- **格式**: `{内部款式编码}-{颜色}-{尺码}`
- **示例**: `AMX0063-白色-M`
- **字段映射**:
  - 飞书 `外部款式编码` → 聚水潭 `supplier_i_id`
  - 飞书 `内部款式编码` → 聚水潭 `i_id`

### 状态管理流转
```
飞书状态: 待处理 → 处理中 → 已完成/失败
```

## ⚠️ 重要注意事项

1. **API调用频率**: 聚水潭有频率限制，成功后延迟0.6秒，遇到199/200错误码等待45秒重试
2. **数据验证**: 所有API调用前必须验证数据完整性
3. **错误日志**: 使用中文记录错误信息，便于问题排查
4. **超时处理**: API调用超时时间设置为20秒
5. **批量处理**: 严格按照API文档的批量限制进行分批处理

参考业务表结构分析: 
- [外采管理表关系分析.md](mdc:reports/外采管理表关系分析.md) - 外采业务流程分析
- [AHMI运营协助更进表_完整分析报告_20250725_115902.md](mdc:reports/AHMI运营协助更进表_完整分析报告_20250725_115902.md) - 完整业务环境和字段详情
