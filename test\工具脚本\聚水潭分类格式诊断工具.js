/**
 * 聚水潭分类格式诊断工具
 *
 * 概述: 诊断聚水潭商品分类格式问题，验证叶子节点要求
 * 详细描述: 分析分类层级结构，识别叶子节点，验证分类格式正确性
 * 调用的函数:
 *   - test\配置数据\聚水潭官方分类配置.json 的配置数据
 * 参数: 无
 * 返回值: 诊断报告和修复建议
 * 修改时间: 2025-07-26 14:07
 */

const fs = require("fs");
const path = require("path");

//------------------
// 配置区域
//------------------

const CONFIG = {
  CATEGORY_CONFIG_PATH: path.join(
    __dirname,
    "../配置数据/聚水潭官方分类配置.json"
  ),
  OUTPUT_PATH: path.join(__dirname, "../配置数据/分类诊断结果.json"),
};

//------------------
// 工具函数
//------------------

/**
 * 日志输出函数
 * @param {string} level - 日志级别
 * @param {string} message - 日志消息
 */
function log(level, message) {
  const timestamp = new Date().toISOString();
  console.log(`${timestamp} [${level}] ${message}`);
}

/**
 * 加载分类配置
 * @returns {Object|null} 分类配置对象
 */
function loadCategoryConfig() {
  try {
    if (!fs.existsSync(CONFIG.CATEGORY_CONFIG_PATH)) {
      log("ERROR", `分类配置文件不存在: ${CONFIG.CATEGORY_CONFIG_PATH}`);
      return null;
    }

    const configContent = fs.readFileSync(CONFIG.CATEGORY_CONFIG_PATH, "utf8");
    const config = JSON.parse(configContent);

    log("INFO", `成功加载分类配置: ${config.total_count}个分类`);
    return config;
  } catch (error) {
    log("ERROR", `加载分类配置失败: ${error.message}`);
    return null;
  }
}

/**
 * 分析分类层级结构
 * @param {Array} categories - 分类数组
 * @returns {Object} 分析结果
 */
function analyzeCategoryHierarchy(categories) {
  const parentMap = new Map(); // 父分类映射
  const childrenMap = new Map(); // 子分类映射
  const leafNodes = []; // 叶子节点
  const parentNodes = []; // 父节点

  // 构建父子关系映射
  categories.forEach((category) => {
    const { c_id, parent_c_id, name } = category;

    if (parent_c_id === 0) {
      // 根节点
      if (!childrenMap.has(c_id)) {
        childrenMap.set(c_id, []);
      }
    } else {
      // 子节点
      parentMap.set(c_id, parent_c_id);

      if (!childrenMap.has(parent_c_id)) {
        childrenMap.set(parent_c_id, []);
      }
      childrenMap.get(parent_c_id).push(category);
    }
  });

  // 识别叶子节点和父节点
  categories.forEach((category) => {
    const { c_id, name } = category;
    const children = childrenMap.get(c_id) || [];

    if (children.length === 0) {
      // 叶子节点（没有子分类）
      leafNodes.push(category);
    } else {
      // 父节点（有子分类）
      parentNodes.push({
        ...category,
        children_count: children.length,
        children_names: children.map((child) => child.name),
      });
    }
  });

  return {
    total_categories: categories.length,
    leaf_nodes: leafNodes,
    parent_nodes: parentNodes,
    leaf_count: leafNodes.length,
    parent_count: parentNodes.length,
  };
}

/**
 * 诊断特定分类
 * @param {string} categoryName - 分类名称
 * @param {Object} analysis - 分析结果
 * @returns {Object} 诊断结果
 */
function diagnoseCategoryName(categoryName, analysis) {
  const { leaf_nodes, parent_nodes } = analysis;

  // 在叶子节点中查找
  const leafMatch = leaf_nodes.find((node) => node.name === categoryName);
  if (leafMatch) {
    return {
      category_name: categoryName,
      is_valid: true,
      node_type: "leaf",
      c_id: leafMatch.c_id,
      parent_c_id: leafMatch.parent_c_id,
      message: `"${categoryName}"是有效的叶子节点，可以直接使用`,
      recommendation: `使用 c_name: "${categoryName}"`,
    };
  }

  // 在父节点中查找
  const parentMatch = parent_nodes.find((node) => node.name === categoryName);
  if (parentMatch) {
    return {
      category_name: categoryName,
      is_valid: false,
      node_type: "parent",
      c_id: parentMatch.c_id,
      parent_c_id: parentMatch.parent_c_id,
      children_count: parentMatch.children_count,
      children_names: parentMatch.children_names,
      message: `"${categoryName}"是父节点，不能直接使用，必须使用其子分类`,
      recommendation: `选择以下子分类之一: ${parentMatch.children_names.join(
        ", "
      )}`,
      suggested_leaf_nodes: parentMatch.children_names,
    };
  }

  return {
    category_name: categoryName,
    is_valid: false,
    node_type: "not_found",
    message: `"${categoryName}"不存在于分类配置中`,
    recommendation: "请检查分类名称是否正确",
  };
}

/**
 * 生成修复建议
 * @param {Object} analysis - 分析结果
 * @returns {Object} 修复建议
 */
function generateFixSuggestions(analysis) {
  const { leaf_nodes, parent_nodes } = analysis;

  // 常用的服装相关叶子节点
  const clothingLeafNodes = leaf_nodes.filter((node) => {
    const name = node.name.toLowerCase();
    return (
      name.includes("衣") ||
      name.includes("裤") ||
      name.includes("裙") ||
      name.includes("t恤") ||
      name.includes("衬衫") ||
      name.includes("外套")
    );
  });

  return {
    total_leaf_nodes: leaf_nodes.length,
    clothing_related_leaf_nodes: clothingLeafNodes.map((node) => ({
      name: node.name,
      c_id: node.c_id,
      usage_example: `c_name: "${node.name}"`,
    })),
    recommended_defaults: [
      { name: "T恤", reason: "通用服装分类" },
      { name: "衬衫", reason: "正装类服装" },
      { name: "外套", reason: "外套类服装" },
      { name: "裤子", reason: "下装类服装" },
    ].filter((item) => leaf_nodes.some((node) => node.name === item.name)),
  };
}

//------------------
// 主要诊断逻辑
//------------------

/**
 * 执行完整诊断
 * @returns {Promise<Object>} 诊断结果
 */
async function runDiagnosis() {
  log("INFO", "=== 开始聚水潭分类格式诊断 ===");

  try {
    // 1. 加载分类配置
    const config = loadCategoryConfig();
    if (!config) {
      throw new Error("无法加载分类配置");
    }

    // 2. 分析分类层级结构
    log("INFO", "分析分类层级结构...");
    const analysis = analyzeCategoryHierarchy(config.full_categories);

    log("INFO", `分析完成: 总计${analysis.total_categories}个分类`);
    log("INFO", `叶子节点: ${analysis.leaf_count}个`);
    log("INFO", `父节点: ${analysis.parent_count}个`);

    // 3. 诊断问题分类"制衣"
    log("INFO", "诊断问题分类'制衣'...");
    const zhiyiDiagnosis = diagnoseCategoryName("制衣", analysis);

    log("INFO", `诊断结果: ${zhiyiDiagnosis.message}`);
    if (zhiyiDiagnosis.suggested_leaf_nodes) {
      log(
        "INFO",
        `建议使用: ${zhiyiDiagnosis.suggested_leaf_nodes.join(", ")}`
      );
    }

    // 4. 生成修复建议
    log("INFO", "生成修复建议...");
    const fixSuggestions = generateFixSuggestions(analysis);

    // 5. 构建完整诊断报告
    const report = {
      diagnosis_time: new Date().toISOString(),
      category_analysis: {
        total_categories: analysis.total_categories,
        leaf_nodes_count: analysis.leaf_count,
        parent_nodes_count: analysis.parent_count,
      },
      problem_diagnosis: {
        tested_category: "制衣",
        result: zhiyiDiagnosis,
      },
      fix_suggestions: fixSuggestions,
      api_requirements: {
        field_name: "c_name",
        requirement: "必须是商品类目管理中的叶子节点",
        error_code: 130,
        error_message:
          "分类【xxxxx】不存在，请修改分类或者重新维护【商品类目管理】",
      },
      recommended_actions: [
        "将脚本中的'制衣'替换为其子分类之一",
        "建议使用'T恤'、'衬衫'或'外套'等叶子节点",
        "更新分类验证逻辑，确保只使用叶子节点",
        "添加分类层级检查功能",
      ],
    };

    // 6. 保存诊断结果
    fs.writeFileSync(
      CONFIG.OUTPUT_PATH,
      JSON.stringify(report, null, 2),
      "utf8"
    );
    log("INFO", `诊断报告已保存: ${CONFIG.OUTPUT_PATH}`);

    return report;
  } catch (error) {
    log("ERROR", `诊断失败: ${error.message}`);
    throw error;
  }
}

//------------------
// 执行诊断
//------------------

if (require.main === module) {
  console.log("脚本开始执行...");
  runDiagnosis()
    .then((report) => {
      log("INFO", "=== 诊断完成 ===");
      log("INFO", `问题确认: ${report.problem_diagnosis.result.message}`);
      log("INFO", `修复建议: ${report.recommended_actions[0]}`);
      console.log("脚本执行完成");
    })
    .catch((error) => {
      log("ERROR", `诊断失败: ${error.message}`);
      console.error("错误详情:", error);
      process.exit(1);
    });
}

module.exports = {
  runDiagnosis,
  diagnoseCategoryName,
  analyzeCategoryHierarchy,
};
