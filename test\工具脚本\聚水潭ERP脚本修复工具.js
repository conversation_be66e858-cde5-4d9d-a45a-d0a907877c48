/**
 * 聚水潭ERP脚本修复工具
 *
 * 功能：
 * 1. 修复分类配置问题
 * 2. 更新商品上传参数格式
 * 3. 改进分类校验逻辑
 * 4. 提供测试和验证功能
 *
 * 修改时间: 2025-01-27
 */

const fs = require("fs");
const path = require("path");

//===================================================================================
// 📋 修复配置
//===================================================================================

const FIXES = {
  // 分类字段修复
  categoryField: {
    // 确保使用正确的字段名
    correctFieldName: "c_name",
    // 备用字段策略
    fallbackStrategy: "empty", // "empty" | "default" | "mapping"
  },

  // 分类名称映射
  categoryMapping: {
    制衣: ["制衣", "服装", "上衣"], // 优先级从高到低
    上衣: ["上衣", "制衣", "服装"],
    裤子: ["裤子", "制衣", "服装"],
    裙子: ["裙子", "制衣", "服装"],
  },

  // 商品参数修复
  productParams: {
    // 确保必填字段
    ensureRequired: ["sku_id", "i_id", "name"],
    // 字段格式规范
    fieldFormat: {
      c_name: "string", // 分类字段
      enabled: "integer", // 启用状态
      properties_value: "string", // 颜色规格
    },
  },
};

//===================================================================================
// 🛠️ 修复函数
//===================================================================================

/**
 * 读取原始脚本文件
 */
function readOriginalScript() {
  try {
    const scriptPath = path.join(__dirname, "../核心脚本/聚水潭ERP集成脚本.js");

    if (!fs.existsSync(scriptPath)) {
      throw new Error(`原始脚本文件不存在: ${scriptPath}`);
    }

    const content = fs.readFileSync(scriptPath, "utf8");
    console.log(`✅ 成功读取原始脚本文件: ${scriptPath}`);
    console.log(`📄 文件大小: ${content.length} 字符`);

    return {
      content,
      path: scriptPath,
    };
  } catch (error) {
    console.error(`❌ 读取原始脚本失败: ${error.message}`);
    return null;
  }
}

/**
 * 加载分类配置
 */
function loadCategoryConfig() {
  try {
    const configPath = path.join(
      __dirname,
      "../配置数据/聚水潭官方分类配置.json"
    );

    if (!fs.existsSync(configPath)) {
      console.warn(`⚠️  分类配置文件不存在: ${configPath}`);
      return null;
    }

    const configContent = fs.readFileSync(configPath, "utf8");
    const config = JSON.parse(configContent);

    console.log(`✅ 成功加载分类配置: ${config.category_names_count} 个分类`);
    return config;
  } catch (error) {
    console.error(`❌ 加载分类配置失败: ${error.message}`);
    return null;
  }
}

/**
 * 分析当前脚本中的分类问题
 */
function analyzeCategoryIssuesInScript(scriptContent, categoryConfig) {
  console.log("🔍 分析脚本中的分类问题...");

  const issues = [];
  const fixes = [];

  // 1. 检查分类字段使用
  if (scriptContent.includes("c_name:") || scriptContent.includes('"c_name"')) {
    console.log("✅ 脚本中使用了正确的分类字段 c_name");
  } else {
    issues.push("脚本中可能没有使用正确的分类字段 c_name");
    fixes.push("确保商品上传时使用 c_name 字段设置分类");
  }

  // 2. 检查分类校验逻辑
  if (scriptContent.includes("validateCategoryName")) {
    console.log("✅ 脚本中包含分类校验逻辑");
  } else {
    issues.push("脚本中缺少分类校验逻辑");
    fixes.push("添加分类校验逻辑");
  }

  // 3. 检查分类映射逻辑
  if (
    scriptContent.includes("categoryToUse") ||
    scriptContent.includes("fallback")
  ) {
    console.log("✅ 脚本中包含分类映射逻辑");
  } else {
    issues.push("脚本中缺少分类映射逻辑");
    fixes.push("添加分类映射和降级逻辑");
  }

  // 4. 检查空分类处理
  if (
    scriptContent.includes('categoryToUse: ""') ||
    scriptContent.includes("categoryToUse: ''")
  ) {
    console.log("✅ 脚本中包含空分类处理");
  } else {
    issues.push("脚本中可能缺少空分类处理");
    fixes.push("添加空分类降级处理");
  }

  // 5. 检查"制衣"分类问题
  if (categoryConfig && categoryConfig.category_names.includes("制衣")) {
    console.log("✅ 本地配置中存在'制衣'分类");
  } else {
    issues.push("本地配置中不存在'制衣'分类");
    fixes.push("更新分类配置或修改分类映射逻辑");
  }

  return { issues, fixes };
}

/**
 * 生成修复后的分类校验函数
 */
function generateFixedCategoryValidation(categoryConfig) {
  const availableCategories = categoryConfig
    ? categoryConfig.category_names
    : [];

  return `
/**
 * 增强的分类校验函数（修复版）
 * @param {string} categoryName - 分类名称
 * @returns {Promise<Object>} 校验结果
 */
async function validateCategoryNameFixed(categoryName) {
  const config = await loadCategoriesConfig();

  if (!config) {
    return {
      valid: true, // 无法校验时允许通过
      message: "无法加载分类配置，跳过分类校验",
      suggestion: "使用空分类名称",
      categoryToUse: "",
    };
  }

  if (!categoryName || !categoryName.trim()) {
    return {
      valid: true,
      message: "使用空分类名称",
      categoryToUse: "",
    };
  }

  const trimmedName = categoryName.trim();
  const validNames = config.category_names || [];

  // 1. 精确匹配
  if (validNames.includes(trimmedName)) {
    return {
      valid: true,
      message: \`分类名称"\${trimmedName}"有效\`,
      categoryToUse: trimmedName,
    };
  }

  // 2. 智能映射
  const categoryMappings = {
    "制衣": ["制衣", "服装", "上衣"],
    "服装": ["服装", "制衣", "上衣"], 
    "上衣": ["上衣", "制衣", "服装"],
    "裤子": ["裤子", "制衣", "服装"],
    "裙子": ["裙子", "制衣", "服装"],
    "外套": ["外套", "上衣", "制衣"],
    "T恤": ["T恤", "上衣", "制衣"],
    "衬衫": ["衬衫", "上衣", "制衣"],
  };

  // 尝试智能映射
  const inputLower = trimmedName.toLowerCase();
  for (const [key, candidates] of Object.entries(categoryMappings)) {
    if (inputLower.includes(key.toLowerCase()) || key.toLowerCase().includes(inputLower)) {
      for (const candidate of candidates) {
        if (validNames.includes(candidate)) {
          return {
            valid: true,
            message: \`分类"\${trimmedName}"映射到"\${candidate}"\`,
            categoryToUse: candidate,
            mapping: true,
          };
        }
      }
    }
  }

  // 3. 模糊匹配
  const similarCategories = validNames.filter(name => 
    name.toLowerCase().includes(inputLower) || 
    inputLower.includes(name.toLowerCase())
  );

  if (similarCategories.length > 0) {
    return {
      valid: true,
      message: \`分类"\${trimmedName}"匹配到相似分类"\${similarCategories[0]}"\`,
      categoryToUse: similarCategories[0],
      suggestions: similarCategories,
      fuzzyMatch: true,
    };
  }

  // 4. 降级处理 - 使用空分类
  console.warn(\`分类"\${trimmedName}"无法匹配，使用空分类\`);
  return {
    valid: true, // 允许通过，但使用空分类
    message: \`分类"\${trimmedName}"不存在，使用空分类\`,
    categoryToUse: "",
    fallback: true,
  };
}`;
}

/**
 * 生成修复后的商品创建函数
 */
function generateFixedProductCreation() {
  return `
/**
 * 增强的商品创建函数（修复版）
 * @param {Array} skuList - SKU列表
 * @param {string} imageUrl - 图片URL
 * @returns {Promise<Array|null>} 验证通过的SKU列表
 */
async function verifyAndCreateProductsFixed(skuList, imageUrl) {
  try {
    log("INFO", "开始验证和创建商品 (修复版)...");

    const verifiedSkus = [];

    for (let i = 0; i < skuList.length; i++) {
      const skuInfo = skuList[i];
      const sku = skuInfo.sku;

      log("INFO", \`验证商品 \${i + 1}/\${skuList.length}: \${sku}\`);

      // 查询商品是否存在
      const queryParams = {
        sku_ids: sku,
        page_index: 1,
        page_size: 1,
      };

      const queryResult = await callJushuitanAPI(
        "/open/sku/query",
        queryParams,
        \`商品查询-\${sku}\`
      );

      let exists = false;
      if (queryResult && queryResult.datas) {
        const goods = queryResult.datas || [];
        exists = goods.length > 0;
      }

      if (!exists) {
        log("INFO", \`商品不存在，开始创建: \${sku}\`);

        // 修复后的商品创建参数
        const createParams = {
          items: [
            {
              sku_id: sku,
              i_id: skuInfo.internalStyleCode,
              name: \`\${skuInfo.internalStyleCode} \${skuInfo.color} \${skuInfo.size}码\`,
              brand: "AHMI",
              c_name: skuInfo.category, // 使用正确的分类字段
              pic: imageUrl || "",
              enabled: 1, // 确保是数字类型
              batch_enabled: false,
              is_series_number: false,
              properties_value: \`\${skuInfo.color};\${skuInfo.size}\`, // 确保格式正确
              s_price: Number(skuInfo.unitPrice) || 0, // 确保是数字类型
              supplier_i_id: skuInfo.supplierStyleCode,
              supplier_sku_id: skuInfo.supplierSku,
              item_type: "成品", // 添加商品属性
            },
          ],
        };

        // 添加详细日志
        log("DEBUG", \`商品创建参数: \${JSON.stringify(createParams, null, 2)}\`);

        const createResult = await callJushuitanAPI(
          "/open/jushuitan/itemsku/upload",
          createParams,
          \`商品创建-\${sku}\`
        );

        if (!createResult || !createResult.datas) {
          log("ERROR", \`商品创建失败: \${sku} - 无响应数据\`);
          continue;
        }

        const createData = createResult.datas[0];
        if (createData && createData.is_success) {
          log("INFO", \`商品创建成功: \${sku}\`);
        } else {
          log("ERROR", \`商品创建失败: \${sku}, 消息: \${createData && createData.msg ? createData.msg : "未知错误"}\`);
          
          // 如果是分类问题，尝试使用空分类重新创建
          if (createData && createData.msg && createData.msg.includes("分类")) {
            log("WARN", \`检测到分类问题，尝试使用空分类重新创建: \${sku}\`);
            
            const retryParams = {
              items: [{
                ...createParams.items[0],
                c_name: "", // 使用空分类
              }],
            };
            
            const retryResult = await callJushuitanAPI(
              "/open/jushuitan/itemsku/upload",
              retryParams,
              \`商品创建重试-\${sku}\`
            );
            
            if (retryResult && retryResult.datas && retryResult.datas[0] && retryResult.datas[0].is_success) {
              log("INFO", \`商品创建重试成功: \${sku} (使用空分类)\`);
              skuInfo.category = ""; // 更新SKU信息
            } else {
              log("ERROR", \`商品创建重试失败: \${sku}\`);
              continue;
            }
          } else {
            continue;
          }
        }
      } else {
        log("INFO", \`商品已存在: \${sku}\`);
      }

      verifiedSkus.push(skuInfo);

      // 添加延迟，避免API频率限制
      await delay(500);
    }

    log("INFO", \`商品验证完成: \${verifiedSkus.length}/\${skuList.length}个商品可用\`);
    return verifiedSkus;
  } catch (error) {
    log("ERROR", \`商品验证失败: \${error.message}\`);
    return null;
  }
}`;
}

/**
 * 应用修复到脚本内容
 */
function applyFixesToScript(scriptContent, categoryConfig) {
  console.log("🔧 应用修复到脚本内容...");

  let fixedContent = scriptContent;

  // 1. 替换分类校验函数
  const newValidationFunction = generateFixedCategoryValidation(categoryConfig);

  // 查找现有的validateCategoryName函数并替换
  const validateFunctionRegex =
    /async function validateCategoryName\([\s\S]*?\n\}/;
  if (validateFunctionRegex.test(fixedContent)) {
    fixedContent = fixedContent.replace(
      validateFunctionRegex,
      newValidationFunction.trim()
    );
    console.log("✅ 替换了分类校验函数");
  } else {
    // 如果没有找到，在适当位置插入
    const insertPosition = fixedContent.indexOf(
      "//===================================================================================\n// 🏭 供应商编码管理功能"
    );
    if (insertPosition > -1) {
      fixedContent =
        fixedContent.slice(0, insertPosition) +
        newValidationFunction +
        "\n\n" +
        fixedContent.slice(insertPosition);
      console.log("✅ 插入了新的分类校验函数");
    }
  }

  // 2. 替换商品创建函数
  const newProductFunction = generateFixedProductCreation();

  // 查找现有的verifyAndCreateProductsEnhanced函数并替换
  const productFunctionRegex =
    /async function verifyAndCreateProductsEnhanced\([\s\S]*?\n\}/;
  if (productFunctionRegex.test(fixedContent)) {
    fixedContent = fixedContent.replace(
      productFunctionRegex,
      newProductFunction.trim()
    );
    console.log("✅ 替换了商品创建函数");
  }

  // 3. 修复测试数据中的分类
  if (categoryConfig && categoryConfig.category_names.includes("制衣")) {
    fixedContent = fixedContent.replace(/分类: "",/, '分类: "制衣",');
    console.log("✅ 修复了测试数据中的分类设置");
  } else if (categoryConfig && categoryConfig.category_names.length > 0) {
    // 使用第一个可用的分类
    const firstCategory = categoryConfig.category_names[0];
    fixedContent = fixedContent.replace(
      /分类: "",/,
      `分类: "${firstCategory}",`
    );
    console.log(`✅ 修复了测试数据中的分类设置，使用: ${firstCategory}`);
  }

  // 4. 添加修复版本标识
  const versionComment = `/**
 * 聚水潭API增强版本 - 分类问题修复版
 * 
 * 修复内容：
 * 1. 修复分类字段使用问题（确保使用c_name）
 * 2. 增强分类校验和映射逻辑
 * 3. 添加分类降级处理（空分类支持）
 * 4. 改进错误处理和重试机制
 * 
 * 修复时间: ${new Date().toISOString()}
 */`;

  // 替换文件头部注释
  const headerRegex = /\/\*\*[\s\S]*?\*\//;
  if (headerRegex.test(fixedContent)) {
    fixedContent = fixedContent.replace(headerRegex, versionComment);
    console.log("✅ 更新了文件头部注释");
  }

  return fixedContent;
}

/**
 * 生成修复报告
 */
function generateFixReport(issues, fixes, categoryConfig) {
  const report = {
    timestamp: new Date().toISOString(),
    issues: issues,
    fixes: fixes,
    categoryConfig: categoryConfig
      ? {
          total_categories: categoryConfig.total_count,
          available_categories: categoryConfig.category_names_count,
          sample_categories: categoryConfig.category_names.slice(0, 10),
        }
      : null,
    recommendations: [
      "测试修复后的脚本前，建议先在测试环境验证",
      "如果仍有分类问题，建议使用空分类或联系管理员",
      "定期更新分类配置文件以保持同步",
      "监控API错误日志，及时发现和解决问题",
    ],
  };

  return report;
}

//===================================================================================
// 🚀 主流程
//===================================================================================

/**
 * 执行完整的修复流程
 */
async function runScriptFix() {
  console.log("🚀 开始聚水潭ERP脚本修复流程...");

  try {
    // 1. 读取原始脚本
    const scriptData = readOriginalScript();
    if (!scriptData) {
      throw new Error("无法读取原始脚本文件");
    }

    // 2. 加载分类配置
    const categoryConfig = loadCategoryConfig();

    // 3. 分析问题
    const analysis = analyzeCategoryIssuesInScript(
      scriptData.content,
      categoryConfig
    );

    console.log("\n📋 发现的问题:");
    analysis.issues.forEach((issue, index) => {
      console.log(`  ${index + 1}. ${issue}`);
    });

    console.log("\n🔧 修复方案:");
    analysis.fixes.forEach((fix, index) => {
      console.log(`  ${index + 1}. ${fix}`);
    });

    // 4. 应用修复
    const fixedContent = applyFixesToScript(scriptData.content, categoryConfig);

    // 5. 保存修复后的脚本
    const fixedScriptPath = path.join(
      __dirname,
      "../核心脚本/聚水潭ERP集成脚本_修复版.js"
    );
    fs.writeFileSync(fixedScriptPath, fixedContent, "utf8");
    console.log(`\n✅ 修复后的脚本已保存到: ${fixedScriptPath}`);

    // 6. 生成修复报告
    const report = generateFixReport(
      analysis.issues,
      analysis.fixes,
      categoryConfig
    );
    const reportPath = path.join(__dirname, "../配置数据/脚本修复报告.json");
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), "utf8");
    console.log(`📄 修复报告已保存到: ${reportPath}`);

    console.log("\n🎉 脚本修复完成!");
    console.log("📝 主要修复内容:");
    console.log("  - 确保使用正确的分类字段 (c_name)");
    console.log("  - 增强分类校验和映射逻辑");
    console.log("  - 添加分类降级处理 (空分类支持)");
    console.log("  - 改进错误处理和重试机制");

    console.log("\n⚠️  使用建议:");
    console.log("  1. 使用修复版脚本替换原脚本");
    console.log("  2. 在测试环境先验证修复效果");
    console.log("  3. 关注分类相关的错误日志");
    console.log("  4. 如仍有问题，考虑使用空分类策略");

    return {
      status: "success",
      fixedScriptPath,
      reportPath,
      report,
    };
  } catch (error) {
    console.error(`❌ 脚本修复失败: ${error.message}`);
    return {
      status: "error",
      message: error.message,
    };
  }
}

// 如果直接执行此文件
if (require.main === module) {
  runScriptFix()
    .then((result) => {
      if (result.status === "success") {
        console.log("\n✅ 修复流程执行成功");
        process.exit(0);
      } else {
        console.log("\n❌ 修复流程执行失败");
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error("修复流程异常:", error);
      process.exit(1);
    });
}

module.exports = {
  runScriptFix,
  readOriginalScript,
  loadCategoryConfig,
  analyzeCategoryIssuesInScript,
  applyFixesToScript,
  generateFixReport,
};
