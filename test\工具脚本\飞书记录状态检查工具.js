/**
 * 飞书记录状态检查工具
 *
 * 模块名称：飞书记录状态检查工具
 * 模块描述：检查特定记录的状态字段值
 * 模块职责：状态验证、字段分析、回写验证
 * 修改时间: 2025-07-26 16:45
 */

const https = require("https");

//===================================================================================
// 📋 配置区域
//===================================================================================

const FEISHU_CONFIG = {
  APP_ID: "cli_a73da8fdc6fe900d",
  APP_SECRET: "CM0Vl3kDoKUHRim3JuJznXT1rqTBbrQa",
  BASE_URL: "https://open.feishu.cn/open-apis",
  BASE_ID: "E1Q3bDq3harjR1s8qr3cyKz6n1b",
  TABLE_ID: "tblwJFuLDpV62Z9p",
};

//===================================================================================
// 🔧 工具函数
//===================================================================================

function log(level, message) {
  const timestamp = new Date().toISOString();
  console.log(`${timestamp} [${level}] ${message}`);
}

async function makeHttpRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname + urlObj.search,
      method: options.method || "GET",
      headers: options.headers || {},
      timeout: options.timeout || 30000,
    };

    const req = https.request(requestOptions, (res) => {
      let data = "";
      res.on("data", (chunk) => { data += chunk; });
      res.on("end", () => {
        try {
          resolve(JSON.parse(data));
        } catch (error) {
          reject(new Error(`JSON解析失败: ${error.message}`));
        }
      });
    });

    req.on("error", reject);
    req.on("timeout", () => {
      req.destroy();
      reject(new Error("请求超时"));
    });

    if (options.body) {
      req.write(options.body);
    }
    req.end();
  });
}

//===================================================================================
// 🔗 飞书API函数
//===================================================================================

async function getFeishuAccessToken() {
  try {
    const url = `${FEISHU_CONFIG.BASE_URL}/auth/v3/tenant_access_token/internal`;
    const data = JSON.stringify({
      app_id: FEISHU_CONFIG.APP_ID,
      app_secret: FEISHU_CONFIG.APP_SECRET,
    });

    const response = await makeHttpRequest(url, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: data,
    });

    if (response && response.code === 0) {
      return response.tenant_access_token;
    }
    throw new Error(`获取token失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `飞书token获取失败: ${error.message}`);
    return null;
  }
}

async function getFeishuRecord(baseId, tableId, recordId) {
  try {
    const token = await getFeishuAccessToken();
    if (!token) throw new Error("获取飞书token失败");

    const url = `${FEISHU_CONFIG.BASE_URL}/bitable/v1/apps/${baseId}/tables/${tableId}/records/${recordId}`;
    
    const response = await makeHttpRequest(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    if (response && response.code === 0) {
      return response.data.record;
    }
    throw new Error(`获取记录失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `飞书记录获取失败: ${error.message}`);
    return null;
  }
}

//===================================================================================
// 🧪 检查函数
//===================================================================================

async function checkRecordStatus(recordId) {
  try {
    log("INFO", `=== 检查记录状态: ${recordId} ===`);
    
    const record = await getFeishuRecord(
      FEISHU_CONFIG.BASE_ID,
      FEISHU_CONFIG.TABLE_ID,
      recordId
    );
    
    if (!record) {
      throw new Error("无法获取记录");
    }
    
    const fields = record.fields;
    
    // 检查关键状态字段
    const statusFields = [
      "流程状态",
      "采购单",
      "采购单号", 
      "处理状态",
      "处理标签",
      "内部款式编码",
      "外部款式编码",
      "档口"
    ];
    
    log("INFO", "=== 状态字段检查 ===");
    
    const statusInfo = {};
    
    for (const fieldName of statusFields) {
      const fieldValue = fields[fieldName];
      
      if (fieldValue !== undefined && fieldValue !== null) {
        let displayValue;
        let rawValue = fieldValue;
        
        // 解析不同类型的字段值
        if (typeof fieldValue === "string") {
          displayValue = fieldValue;
        } else if (Array.isArray(fieldValue)) {
          if (fieldValue.length === 0) {
            displayValue = "(空数组)";
          } else if (fieldValue[0] && fieldValue[0].text) {
            displayValue = fieldValue.map(item => item.text).join(", ");
          } else if (fieldValue[0] && typeof fieldValue[0] === "string") {
            displayValue = fieldValue.join(", ");
          } else {
            displayValue = `(数组: ${fieldValue.length}项)`;
          }
        } else if (typeof fieldValue === "object") {
          if (fieldValue.text) {
            displayValue = fieldValue.text;
          } else if (fieldValue.text_arr) {
            displayValue = fieldValue.text_arr.length > 0 ? 
              fieldValue.text_arr.join(", ") : "(关联字段: 无关联)";
          } else {
            displayValue = "(对象)";
          }
        } else {
          displayValue = String(fieldValue);
        }
        
        statusInfo[fieldName] = {
          display: displayValue,
          raw: rawValue,
          type: typeof fieldValue,
          isArray: Array.isArray(fieldValue)
        };
        
        log("INFO", `  ${fieldName}: ${displayValue}`);
      } else {
        statusInfo[fieldName] = {
          display: "(未设置)",
          raw: fieldValue,
          type: typeof fieldValue
        };
        log("INFO", `  ${fieldName}: (未设置)`);
      }
    }
    
    // 输出详细分析
    console.log("\n=== 详细状态分析 ===");
    console.log(JSON.stringify(statusInfo, null, 2));
    
    // 检查采购单回写状态
    log("INFO", "\n=== 采购单回写状态分析 ===");
    
    const 流程状态 = statusInfo["流程状态"];
    const 采购单 = statusInfo["采购单"];
    
    if (流程状态 && 流程状态.display !== "(未设置)") {
      if (流程状态.display === "在途中") {
        log("INFO", "✅ 流程状态已更新为'在途中'，采购单创建成功");
      } else {
        log("WARN", `⚠️ 流程状态为'${流程状态.display}'，可能未正确更新`);
      }
    } else {
      log("WARN", "⚠️ 流程状态字段未设置");
    }
    
    if (采购单 && 采购单.display !== "(未设置)" && 采购单.display !== "(关联字段: 无关联)") {
      log("INFO", "✅ 采购单字段有值，回写成功");
    } else {
      log("WARN", "⚠️ 采购单字段无值或为关联字段");
    }
    
    return statusInfo;
  } catch (error) {
    log("ERROR", `状态检查失败: ${error.message}`);
    return null;
  }
}

//===================================================================================
// 🚀 主函数
//===================================================================================

async function main() {
  try {
    // 检查测试记录
    const testRecordId = "recuRdw2zCUtBE";
    const statusInfo = await checkRecordStatus(testRecordId);
    
    if (statusInfo) {
      log("INFO", "=== 状态检查完成 ===");
      
      // 总结检查结果
      const hasFlowStatus = statusInfo["流程状态"] && statusInfo["流程状态"].display !== "(未设置)";
      const hasPurchaseOrder = statusInfo["采购单"] && statusInfo["采购单"].display !== "(未设置)" && statusInfo["采购单"].display !== "(关联字段: 无关联)";
      
      if (hasFlowStatus && hasPurchaseOrder) {
        log("INFO", "✅ 飞书回写功能正常工作");
      } else if (hasFlowStatus) {
        log("WARN", "⚠️ 流程状态已更新，但采购单字段可能有问题");
      } else if (hasPurchaseOrder) {
        log("WARN", "⚠️ 采购单字段已更新，但流程状态可能有问题");
      } else {
        log("ERROR", "❌ 飞书回写功能可能存在问题");
      }
    }
  } catch (error) {
    log("ERROR", `检查执行失败: ${error.message}`);
  }
}

// 执行检查
if (require.main === module) {
  main();
}
