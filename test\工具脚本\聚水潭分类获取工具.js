/**
 * 聚水潭商品类目获取脚本 (官方API版本)
 *
 * 功能：使用聚水潭官方 /open/category/query 接口获取所有商品类目
 * 用途：为商品上传时的分类校验提供准确的分类列表
 */

//===================================================================================
// 📋 配置参数区
//===================================================================================

// 聚水潭ERP API配置
const JUSHUITAN_CONFIG = {
  APP_KEY: "13a2701994bd4230a1ed9a12302ba30a",
  APP_SECRET: "f6e5dd9d168d4817973cb42f121218a0",
  ACCESS_TOKEN: "9a9d33072cc8450b916b7c8dd830d22c",
  BASE_URL: "https://openapi.jushuitan.com",
  API_TIMEOUT: 30000,
  RETRY_TIMES: 3,
  RETRY_DELAY: 1000,
};

//===================================================================================
// 🛠️ 工具函数区
//===================================================================================

/**
 * 简单的控制台日志函数
 */
function log(level, ...messages) {
  const timestamp = new Date().toISOString();
  const logMessage = `${timestamp} [${level.toUpperCase()}] ${messages.join(
    " "
  )}`;
  console.log(logMessage);
}

/**
 * 计算MD5哈希值
 */
async function calculateMD5(str) {
  if (typeof require !== "undefined") {
    const crypto = require("crypto");
    return crypto.createHash("md5").update(str).digest("hex");
  }
}

/**
 * 延迟函数
 */
function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

//===================================================================================
// 🔧 API调用函数
//===================================================================================

/**
 * 构建聚水潭API参数
 */
function buildJushuitanParams(bizParams) {
  const params = {
    access_token: JUSHUITAN_CONFIG.ACCESS_TOKEN,
    app_key: JUSHUITAN_CONFIG.APP_KEY,
    charset: "utf-8",
    timestamp: Math.floor(Date.now() / 1000),
    version: "2",
    biz: JSON.stringify(bizParams),
  };

  // 按ASCII码排序
  const sortedParams = {};
  const keys = Object.keys(params).sort();
  keys.forEach((key) => {
    sortedParams[key] = params[key];
  });

  return sortedParams;
}

/**
 * 生成聚水潭API签名
 */
async function generateJushuitanSignature(params) {
  try {
    const filteredKeys = Object.keys(params)
      .filter((key) => key !== "sign")
      .sort();

    const signParts = filteredKeys.map((key) => key + params[key]);
    const signString = JUSHUITAN_CONFIG.APP_SECRET + signParts.join("");

    return await calculateMD5(signString);
  } catch (error) {
    log("ERROR", `签名生成失败: ${error.message}`);
    return null;
  }
}

/**
 * 调用聚水潭API
 */
async function callJushuitanAPI(apiPath, bizParams, apiName) {
  const apiUrl = `${JUSHUITAN_CONFIG.BASE_URL}${apiPath}`;

  try {
    log("INFO", `调用聚水潭API: ${apiName}`);

    // 构建参数
    const params = buildJushuitanParams(bizParams);
    const sign = await generateJushuitanSignature(params);
    if (!sign) {
      throw new Error("签名生成失败");
    }
    params.sign = sign;

    // 构建请求体
    const formData = new URLSearchParams();
    Object.keys(params).forEach((key) => {
      formData.append(key, params[key]);
    });

    log("DEBUG", `请求URL: ${apiUrl}`);

    const controller = new AbortController();
    const timeoutId = setTimeout(
      () => controller.abort(),
      JUSHUITAN_CONFIG.API_TIMEOUT
    );

    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: formData,
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (response.ok) {
      const responseText = await response.text();
      log("DEBUG", `响应内容: ${responseText}`);

      const result = JSON.parse(responseText);

      if (result.code === 0) {
        log("INFO", `${apiName} 请求成功`);
        return result.data;
      } else {
        log(
          "ERROR",
          `${apiName} 错误响应: ${result.msg} (code: ${result.code})`
        );
        return null;
      }
    } else {
      throw new Error(`HTTP请求失败: ${response.status}`);
    }
  } catch (error) {
    log("ERROR", `聚水潭API调用失败 [${apiName}]: ${error.message}`);
    return null;
  }
}

//===================================================================================
// 🎯 类目获取逻辑
//===================================================================================

/**
 * 获取所有商品类目
 */
async function getProductCategories() {
  log("INFO", "开始获取商品类目...");

  const allCategories = [];
  let pageIndex = 1;
  const pageSize = 100;
  let hasMore = true;
  let totalCategories = 0;

  while (hasMore) {
    try {
      log("INFO", `正在查询第 ${pageIndex} 页类目数据...`);

      const queryParams = {
        page_index: pageIndex,
        page_size: pageSize,
        c_ids: [],
        parent_c_ids: [],
      };

      const result = await callJushuitanAPI(
        "/open/category/query",
        queryParams,
        `类目查询-第${pageIndex}页`
      );

      if (!result) {
        log("WARN", `第 ${pageIndex} 页查询失败，跳过`);
        break;
      }

      const categories = result.datas || [];
      totalCategories += categories.length;

      // 收集类目信息
      categories.forEach((category) => {
        allCategories.push({
          c_id: category.c_id,
          parent_c_id: category.parent_c_id,
          name: category.name,
          modified: category.modified,
        });
      });

      log(
        "INFO",
        `第 ${pageIndex} 页: ${categories.length} 个类目，累计: ${totalCategories} 个`
      );

      // 检查是否还有下一页
      hasMore = result.has_next;
      pageIndex++;

      // 添加延迟避免API频率限制
      if (hasMore) {
        await delay(500);
      }
    } catch (error) {
      log("ERROR", `查询第 ${pageIndex} 页时发生错误: ${error.message}`);
      break;
    }
  }

  log("INFO", `类目获取完成! 共获取 ${totalCategories} 个类目`);
  return allCategories;
}

/**
 * 构建类目树结构
 */
function buildCategoryTree(categories) {
  const categoryMap = new Map();
  const rootCategories = [];

  // 先建立ID映射
  categories.forEach((category) => {
    categoryMap.set(category.c_id, {
      ...category,
      children: [],
    });
  });

  // 构建树结构
  categories.forEach((category) => {
    const categoryNode = categoryMap.get(category.c_id);

    if (category.parent_c_id && categoryMap.has(category.parent_c_id)) {
      // 有父节点，添加到父节点的children
      const parentNode = categoryMap.get(category.parent_c_id);
      parentNode.children.push(categoryNode);
    } else {
      // 没有父节点或父节点不存在，作为根节点
      rootCategories.push(categoryNode);
    }
  });

  return rootCategories;
}

/**
 * 提取所有类目名称（扁平化）
 */
function extractCategoryNames(categories) {
  const names = new Set();

  categories.forEach((category) => {
    if (category.name && category.name.trim()) {
      names.add(category.name.trim());
    }
  });

  return Array.from(names).sort();
}

/**
 * 保存类目配置到文件
 */
async function saveCategoriesConfig(categories, categoryTree) {
  const categoryNames = extractCategoryNames(categories);

  const config = {
    update_time: new Date().toISOString(),
    total_count: categories.length,
    category_names_count: categoryNames.length,
    // 完整类目数据
    full_categories: categories,
    // 树结构
    category_tree: categoryTree,
    // 类目名称列表（用于校验）
    category_names: categoryNames,
    // 使用说明
    usage: {
      description: "聚水潭商品类目配置",
      category_names: "用于商品上传时的分类名称校验",
      full_categories: "完整的类目数据，包含ID和父子关系",
      category_tree: "层级结构的类目树",
    },
  };

  if (typeof require !== "undefined") {
    const fs = require("fs");
    const filePath = "./jushuitan_categories_official.json";
    fs.writeFileSync(filePath, JSON.stringify(config, null, 2), "utf8");
    log("INFO", `类目配置已保存到: ${filePath}`);
  }

  return config;
}

/**
 * 显示类目统计
 */
function displayCategoriesStats(categories, categoryTree) {
  log("INFO", "=== 商品类目统计 ===");
  log("INFO", `总类目数: ${categories.length}`);
  log("INFO", `根类目数: ${categoryTree.length}`);

  const categoryNames = extractCategoryNames(categories);
  log("INFO", `唯一类目名称数: ${categoryNames.length}`);

  if (categoryNames.length > 0) {
    log("INFO", "所有类目名称:");
    categoryNames.forEach((name, index) => {
      console.log(`  ${index + 1}. ${name}`);
    });
  } else {
    log("WARN", "未发现任何类目");
  }

  // 显示根类目结构
  log("INFO", "\n=== 根类目结构 ===");
  categoryTree.forEach((rootCategory, index) => {
    console.log(
      `${index + 1}. ${rootCategory.name} (ID: ${rootCategory.c_id})`
    );
    if (rootCategory.children.length > 0) {
      rootCategory.children.forEach((child) => {
        console.log(`   └─ ${child.name} (ID: ${child.c_id})`);
      });
    }
  });
}

/**
 * 检查分类名称是否有效
 */
function validateCategoryName(categoryName, validNames) {
  if (!categoryName || !categoryName.trim()) {
    return { valid: false, message: "分类名称不能为空" };
  }

  const trimmedName = categoryName.trim();
  if (validNames.includes(trimmedName)) {
    return { valid: true, message: "分类名称有效" };
  } else {
    return {
      valid: false,
      message: `分类名称"${trimmedName}"不存在，可用分类: ${validNames
        .slice(0, 5)
        .join(", ")}${validNames.length > 5 ? "..." : ""}`,
    };
  }
}

//===================================================================================
// 🚀 主函数
//===================================================================================

/**
 * 主函数
 */
async function main() {
  try {
    log("INFO", "聚水潭商品类目获取工具启动...");

    // 1. 获取类目数据
    const categories = await getProductCategories();
    if (!categories || categories.length === 0) {
      throw new Error("未获取到任何类目数据");
    }

    // 2. 构建类目树
    const categoryTree = buildCategoryTree(categories);

    // 3. 显示统计
    displayCategoriesStats(categories, categoryTree);

    // 4. 保存配置
    const config = await saveCategoriesConfig(categories, categoryTree);

    // 5. 测试分类校验功能
    log("INFO", "\n=== 测试分类校验 ===");
    const categoryNames = extractCategoryNames(categories);

    // 测试有效分类
    if (categoryNames.length > 0) {
      const testValid = validateCategoryName(categoryNames[0], categoryNames);
      log("INFO", `测试有效分类"${categoryNames[0]}": ${testValid.message}`);
    }

    // 测试无效分类
    const testInvalid = validateCategoryName("不存在的分类", categoryNames);
    log("INFO", `测试无效分类"不存在的分类": ${testInvalid.message}`);

    log("INFO", "类目获取完成!");
    return config;
  } catch (error) {
    log("ERROR", `执行发生错误: ${error.message}`);
    return null;
  }
}

//===================================================================================
// 🔧 模块导出和直接执行支持
//===================================================================================

// Node.js环境直接执行
if (typeof require !== "undefined" && require.main === module) {
  main()
    .then((result) => {
      if (result) {
        console.log("\n=== 执行结果 ===");
        console.log(`获取到 ${result.total_count} 个类目`);
        console.log(`可用类目名称 ${result.category_names_count} 个`);
        console.log("配置文件已生成: jushuitan_categories_official.json");
        process.exit(0);
      } else {
        console.log("执行失败");
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error("执行错误:", error);
      process.exit(1);
    });
}

// 模块导出
if (typeof module !== "undefined" && module.exports) {
  module.exports = {
    main,
    getProductCategories,
    buildCategoryTree,
    extractCategoryNames,
    validateCategoryName,
    saveCategoriesConfig,
    callJushuitanAPI,
  };
}
