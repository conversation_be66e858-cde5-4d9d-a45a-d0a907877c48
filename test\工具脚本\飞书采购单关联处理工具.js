/**
 * 飞书采购单关联处理工具
 *
 * 模块名称：飞书采购单关联处理工具
 * 模块描述：处理飞书采购单关联字段的创建和更新
 * 模块职责：外采清单记录创建、关联字段更新、双向关联管理
 * 修改时间: 2025-07-26 17:00
 */

const https = require("https");

//===================================================================================
// 📋 配置区域
//===================================================================================

const FEISHU_CONFIG = {
  APP_ID: "cli_a73da8fdc6fe900d",
  APP_SECRET: "CM0Vl3kDoKUHRim3JuJznXT1rqTBbrQa",
  BASE_URL: "https://open.feishu.cn/open-apis",
  BASE_ID: "E1Q3bDq3harjR1s8qr3cyKz6n1b",
  MAIN_TABLE_ID: "tblwJFuLDpV62Z9p", // 主表：外采下单表
  PURCHASE_TABLE_ID: "tblCgMR5F7T3gicd", // 外采清单表（已确认）
};

//===================================================================================
// 🔧 工具函数
//===================================================================================

function log(level, message) {
  const timestamp = new Date().toISOString();
  console.log(`${timestamp} [${level}] ${message}`);
}

async function makeHttpRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname + urlObj.search,
      method: options.method || "GET",
      headers: options.headers || {},
      timeout: options.timeout || 30000,
    };

    const req = https.request(requestOptions, (res) => {
      let data = "";
      res.on("data", (chunk) => {
        data += chunk;
      });
      res.on("end", () => {
        try {
          resolve(JSON.parse(data));
        } catch (error) {
          reject(new Error(`JSON解析失败: ${error.message}`));
        }
      });
    });

    req.on("error", reject);
    req.on("timeout", () => {
      req.destroy();
      reject(new Error("请求超时"));
    });

    if (options.body) {
      req.write(options.body);
    }
    req.end();
  });
}

//===================================================================================
// 🔗 飞书API函数
//===================================================================================

async function getFeishuAccessToken() {
  try {
    const url = `${FEISHU_CONFIG.BASE_URL}/auth/v3/tenant_access_token/internal`;
    const data = JSON.stringify({
      app_id: FEISHU_CONFIG.APP_ID,
      app_secret: FEISHU_CONFIG.APP_SECRET,
    });

    const response = await makeHttpRequest(url, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: data,
    });

    if (response && response.code === 0) {
      return response.tenant_access_token;
    }
    throw new Error(`获取token失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `飞书token获取失败: ${error.message}`);
    return null;
  }
}

/**
 * 获取外采清单表的结构
 */
async function getPurchaseTableStructure() {
  try {
    log("INFO", "获取外采清单表结构...");

    const token = await getFeishuAccessToken();
    if (!token) throw new Error("获取飞书token失败");

    // 获取表格字段信息
    const url = `${FEISHU_CONFIG.BASE_URL}/bitable/v1/apps/${FEISHU_CONFIG.BASE_ID}/tables/${FEISHU_CONFIG.PURCHASE_TABLE_ID}/fields`;

    const response = await makeHttpRequest(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    if (response && response.code === 0) {
      return response.data.items;
    }
    throw new Error(`获取表结构失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `外采清单表结构获取失败: ${error.message}`);
    return null;
  }
}

/**
 * 在外采清单表中创建采购单记录
 */
async function createPurchaseOrderRecord(purchaseOrderData) {
  try {
    log("INFO", "在外采清单表中创建采购单记录...");

    const token = await getFeishuAccessToken();
    if (!token) throw new Error("获取飞书token失败");

    const url = `${FEISHU_CONFIG.BASE_URL}/bitable/v1/apps/${FEISHU_CONFIG.BASE_ID}/tables/${FEISHU_CONFIG.PURCHASE_TABLE_ID}/records`;

    const recordData = {
      fields: {
        采购单: purchaseOrderData.purchaseOrderId,
        采购单日期: Date.now(),
        采购人: "系统自动",
        核账结算金额: purchaseOrderData.totalAmount
          ? String(purchaseOrderData.totalAmount)
          : "0",
        实付金额: purchaseOrderData.totalAmount || 0,
        是否已付: false,
        是否已核销: false,
        // 备注字段在外采清单表中可能不存在，先注释掉
        // "备注": `自动创建 - 来源记录: ${purchaseOrderData.sourceRecordId}`,
      },
    };

    const response = await makeHttpRequest(url, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(recordData),
    });

    if (response && response.code === 0) {
      log("INFO", `外采清单记录创建成功: ${response.data.record.record_id}`);
      return response.data.record.record_id;
    }
    throw new Error(`创建记录失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `外采清单记录创建失败: ${error.message}`);
    return null;
  }
}

/**
 * 更新主表记录的采购单关联字段
 */
async function updateMainRecordPurchaseLink(recordId, purchaseRecordId) {
  try {
    log("INFO", `更新主表记录关联字段: ${recordId} -> ${purchaseRecordId}`);

    const token = await getFeishuAccessToken();
    if (!token) throw new Error("获取飞书token失败");

    const url = `${FEISHU_CONFIG.BASE_URL}/bitable/v1/apps/${FEISHU_CONFIG.BASE_ID}/tables/${FEISHU_CONFIG.MAIN_TABLE_ID}/records/${recordId}`;

    const updateData = {
      fields: {
        采购单: {
          link_record_ids: [purchaseRecordId],
        },
        流程状态: "在途中", // 同时更新流程状态
      },
    };

    const response = await makeHttpRequest(url, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(updateData),
    });

    if (response && response.code === 0) {
      log("INFO", "主表记录关联字段更新成功");
      return true;
    }
    throw new Error(`更新关联字段失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `主表记录关联字段更新失败: ${error.message}`);
    return false;
  }
}

/**
 * 完整的采购单关联处理流程
 */
async function processPurchaseOrderLink(sourceRecordId, purchaseOrderData) {
  try {
    log("INFO", "=== 开始采购单关联处理流程 ===");

    // 1. 获取外采清单表结构（用于验证字段）
    const tableStructure = await getPurchaseTableStructure();
    if (!tableStructure) {
      throw new Error("无法获取外采清单表结构");
    }

    log("INFO", `外采清单表包含 ${tableStructure.length} 个字段`);

    // 2. 在外采清单表中创建采购单记录
    const purchaseRecordId = await createPurchaseOrderRecord({
      ...purchaseOrderData,
      sourceRecordId: sourceRecordId,
    });

    if (!purchaseRecordId) {
      throw new Error("外采清单记录创建失败");
    }

    // 3. 更新主表记录的关联字段
    const linkSuccess = await updateMainRecordPurchaseLink(
      sourceRecordId,
      purchaseRecordId
    );

    if (!linkSuccess) {
      throw new Error("主表关联字段更新失败");
    }

    log("INFO", "=== 采购单关联处理流程完成 ===");

    return {
      success: true,
      purchaseRecordId: purchaseRecordId,
      message: "采购单关联处理成功",
    };
  } catch (error) {
    log("ERROR", `采购单关联处理失败: ${error.message}`);
    return {
      success: false,
      message: error.message,
    };
  }
}

//===================================================================================
// 🧪 测试函数
//===================================================================================

async function testPurchaseOrderLink() {
  try {
    log("INFO", "=== 开始测试采购单关联处理 ===");

    // 模拟采购单数据
    const testPurchaseData = {
      purchaseOrderId: "PO-TEST-001",
      supplierName: "测试供应商",
      totalAmount: 1000.0,
      items: [
        { sku: "TEST-SKU-001", quantity: 10, price: 50.0 },
        { sku: "TEST-SKU-002", quantity: 20, price: 25.0 },
      ],
    };

    // 测试记录ID
    const testRecordId = "recuRdw2zCUtBE";

    const result = await processPurchaseOrderLink(
      testRecordId,
      testPurchaseData
    );

    console.log("\n=== 测试结果 ===");
    console.log(JSON.stringify(result, null, 2));

    if (result.success) {
      log("INFO", "✅ 采购单关联处理测试成功");
    } else {
      log("WARN", "⚠️ 采购单关联处理测试失败");
    }

    return result;
  } catch (error) {
    log("ERROR", `测试执行失败: ${error.message}`);
    return null;
  }
}

//===================================================================================
// 🚀 主函数
//===================================================================================

async function main() {
  try {
    log("INFO", "=== 开始飞书采购单关联处理工具测试 ===");

    // 首先获取外采清单表结构
    const structure = await getPurchaseTableStructure();

    if (structure) {
      log("INFO", "外采清单表字段列表:");
      structure.forEach((field, index) => {
        log(
          "INFO",
          `  ${index + 1}. ${field.field_name} (${field.type}) - ${
            field.field_id
          }`
        );
      });
    }

    // 然后测试关联处理
    await testPurchaseOrderLink();
  } catch (error) {
    log("ERROR", `主函数执行失败: ${error.message}`);
    console.error("详细错误信息:", error);
  }
}

// 导出函数
module.exports = {
  processPurchaseOrderLink,
  createPurchaseOrderRecord,
  updateMainRecordPurchaseLink,
  getPurchaseTableStructure,
};

// 执行测试
if (require.main === module) {
  main();
}
