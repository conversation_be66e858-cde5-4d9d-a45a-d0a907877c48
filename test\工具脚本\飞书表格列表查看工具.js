/**
 * 飞书表格列表查看工具
 *
 * 模块名称：飞书表格列表查看工具
 * 模块描述：查看飞书多维表格中的所有表格
 * 模块职责：表格发现、ID获取、结构分析
 * 修改时间: 2025-07-26 17:05
 */

const https = require("https");

//===================================================================================
// 📋 配置区域
//===================================================================================

const FEISHU_CONFIG = {
  APP_ID: "cli_a73da8fdc6fe900d",
  APP_SECRET: "CM0Vl3kDoKUHRim3JuJznXT1rqTBbrQa",
  BASE_URL: "https://open.feishu.cn/open-apis",
  BASE_ID: "E1Q3bDq3harjR1s8qr3cyKz6n1b",
};

//===================================================================================
// 🔧 工具函数
//===================================================================================

function log(level, message) {
  const timestamp = new Date().toISOString();
  console.log(`${timestamp} [${level}] ${message}`);
}

async function makeHttpRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname + urlObj.search,
      method: options.method || "GET",
      headers: options.headers || {},
      timeout: options.timeout || 30000,
    };

    const req = https.request(requestOptions, (res) => {
      let data = "";
      res.on("data", (chunk) => { data += chunk; });
      res.on("end", () => {
        try {
          resolve(JSON.parse(data));
        } catch (error) {
          reject(new Error(`JSON解析失败: ${error.message}`));
        }
      });
    });

    req.on("error", reject);
    req.on("timeout", () => {
      req.destroy();
      reject(new Error("请求超时"));
    });

    if (options.body) {
      req.write(options.body);
    }
    req.end();
  });
}

//===================================================================================
// 🔗 飞书API函数
//===================================================================================

async function getFeishuAccessToken() {
  try {
    const url = `${FEISHU_CONFIG.BASE_URL}/auth/v3/tenant_access_token/internal`;
    const data = JSON.stringify({
      app_id: FEISHU_CONFIG.APP_ID,
      app_secret: FEISHU_CONFIG.APP_SECRET,
    });

    const response = await makeHttpRequest(url, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: data,
    });

    if (response && response.code === 0) {
      return response.tenant_access_token;
    }
    throw new Error(`获取token失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `飞书token获取失败: ${error.message}`);
    return null;
  }
}

/**
 * 获取多维表格中的所有表格
 */
async function getAllTables() {
  try {
    log("INFO", "获取多维表格中的所有表格...");
    
    const token = await getFeishuAccessToken();
    if (!token) throw new Error("获取飞书token失败");

    const url = `${FEISHU_CONFIG.BASE_URL}/bitable/v1/apps/${FEISHU_CONFIG.BASE_ID}/tables`;
    
    const response = await makeHttpRequest(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    if (response && response.code === 0) {
      return response.data.items;
    }
    throw new Error(`获取表格列表失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `表格列表获取失败: ${error.message}`);
    return null;
  }
}

/**
 * 获取指定表格的字段信息
 */
async function getTableFields(tableId) {
  try {
    const token = await getFeishuAccessToken();
    if (!token) throw new Error("获取飞书token失败");

    const url = `${FEISHU_CONFIG.BASE_URL}/bitable/v1/apps/${FEISHU_CONFIG.BASE_ID}/tables/${tableId}/fields`;
    
    const response = await makeHttpRequest(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    if (response && response.code === 0) {
      return response.data.items;
    }
    return null;
  } catch (error) {
    log("WARN", `表格 ${tableId} 字段获取失败: ${error.message}`);
    return null;
  }
}

/**
 * 分析所有表格
 */
async function analyzeAllTables() {
  try {
    log("INFO", "=== 开始分析多维表格中的所有表格 ===");
    
    const tables = await getAllTables();
    if (!tables) {
      throw new Error("无法获取表格列表");
    }
    
    log("INFO", `发现 ${tables.length} 个表格:`);
    
    const tableAnalysis = {};
    
    for (let i = 0; i < tables.length; i++) {
      const table = tables[i];
      log("INFO", `\n--- 分析表格 ${i + 1}: ${table.name} ---`);
      log("INFO", `表格ID: ${table.table_id}`);
      log("INFO", `修订版本: ${table.revision}`);
      
      // 获取字段信息
      const fields = await getTableFields(table.table_id);
      
      if (fields) {
        log("INFO", `字段数量: ${fields.length}`);
        log("INFO", "字段列表:");
        
        const fieldInfo = [];
        fields.forEach((field, index) => {
          const fieldDesc = `${field.field_name} (${field.type})`;
          log("INFO", `  ${index + 1}. ${fieldDesc}`);
          fieldInfo.push({
            name: field.field_name,
            type: field.type,
            id: field.field_id
          });
        });
        
        tableAnalysis[table.name] = {
          table_id: table.table_id,
          revision: table.revision,
          field_count: fields.length,
          fields: fieldInfo
        };
      } else {
        log("WARN", `无法获取表格 ${table.name} 的字段信息`);
        tableAnalysis[table.name] = {
          table_id: table.table_id,
          revision: table.revision,
          field_count: 0,
          fields: []
        };
      }
    }
    
    // 输出完整分析结果
    console.log("\n=== 表格分析结果 ===");
    console.log(JSON.stringify(tableAnalysis, null, 2));
    
    // 查找可能的采购单相关表格
    log("INFO", "\n=== 采购单相关表格分析 ===");
    
    Object.entries(tableAnalysis).forEach(([tableName, info]) => {
      const hasOrderFields = info.fields.some(field => 
        field.name.includes("采购") || 
        field.name.includes("订单") || 
        field.name.includes("清单")
      );
      
      if (hasOrderFields || tableName.includes("采购") || tableName.includes("清单")) {
        log("INFO", `🎯 可能的采购单表格: ${tableName} (${info.table_id})`);
        log("INFO", `   相关字段: ${info.fields.filter(f => 
          f.name.includes("采购") || f.name.includes("订单") || f.name.includes("清单")
        ).map(f => f.name).join(", ")}`);
      }
    });
    
    return tableAnalysis;
  } catch (error) {
    log("ERROR", `表格分析失败: ${error.message}`);
    return null;
  }
}

//===================================================================================
// 🚀 主函数
//===================================================================================

async function main() {
  try {
    const analysis = await analyzeAllTables();
    
    if (analysis) {
      log("INFO", "=== 表格分析完成 ===");
      
      const tableNames = Object.keys(analysis);
      log("INFO", `总共发现 ${tableNames.length} 个表格: ${tableNames.join(", ")}`);
    }
  } catch (error) {
    log("ERROR", `分析执行失败: ${error.message}`);
  }
}

// 执行分析
if (require.main === module) {
  main();
}
