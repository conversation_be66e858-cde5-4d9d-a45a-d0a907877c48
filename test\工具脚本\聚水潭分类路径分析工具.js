/**
 * 聚水潭分类路径发现脚本
 *
 * 功能：分析分类树结构，生成正确的分类路径格式
 */

// 加载类目配置
const fs = require("fs");
const path = require("path");

function loadCategoriesConfig() {
  const configPath = path.join(__dirname, "jushuitan_categories_official.json");
  const configContent = fs.readFileSync(configPath, "utf8");
  return JSON.parse(configContent);
}

/**
 * 构建分类路径映射
 */
function buildCategoryPaths(categories) {
  const categoryMap = new Map();
  const pathMap = new Map();

  // 建立ID到分类的映射
  categories.forEach((category) => {
    categoryMap.set(category.c_id, category);
  });

  // 递归构建路径
  function buildPath(categoryId, visited = new Set()) {
    if (visited.has(categoryId)) {
      return "循环引用";
    }

    const category = categoryMap.get(categoryId);
    if (!category) {
      return "未知分类";
    }

    visited.add(categoryId);

    // 如果是根分类
    if (!category.parent_c_id || category.parent_c_id === 0) {
      return category.name;
    }

    // 递归获取父级路径
    const parentPath = buildPath(category.parent_c_id, visited);
    return `${parentPath}-${category.name}`;
  }

  // 为每个分类生成路径
  categories.forEach((category) => {
    const fullPath = buildPath(category.c_id);
    pathMap.set(category.name, fullPath);
    pathMap.set(category.c_id, fullPath);
  });

  return pathMap;
}

/**
 * 分析常用服装分类路径
 */
function analyzeClothingCategories(pathMap, categories) {
  console.log("=== 服装相关分类路径分析 ===\n");

  const clothingRelated = [];

  categories.forEach((category) => {
    const fullPath = pathMap.get(category.name);

    // 查找服装相关的分类
    if (
      fullPath.includes("服装") ||
      fullPath.includes("制衣") ||
      fullPath.includes("上衣") ||
      fullPath.includes("裤子") ||
      fullPath.includes("裙") ||
      fullPath.includes("T恤") ||
      fullPath.includes("衬衫")
    ) {
      clothingRelated.push({
        name: category.name,
        path: fullPath,
        id: category.c_id,
        parent_id: category.parent_c_id,
      });
    }
  });

  // 按路径长度排序
  clothingRelated.sort((a, b) => a.path.length - b.path.length);

  clothingRelated.forEach((item, index) => {
    console.log(`${index + 1}. ${item.name}`);
    console.log(`   完整路径: ${item.path}`);
    console.log(`   ID: ${item.id}, 父级ID: ${item.parent_id}`);
    console.log("");
  });

  return clothingRelated;
}

/**
 * 生成推荐的分类路径配置
 */
function generateRecommendedPaths(clothingCategories) {
  console.log("=== 推荐的商品分类路径 ===\n");

  const recommendations = {
    常用服装分类: [],
    上衣类: [],
    下装类: [],
    连体类: [],
  };

  clothingCategories.forEach((item) => {
    if (
      item.path.includes("T恤") ||
      item.path.includes("衬衫") ||
      item.path.includes("卫衣")
    ) {
      recommendations["上衣类"].push(item.path);
    } else if (item.path.includes("裤") || item.path.includes("短裤")) {
      recommendations["下装类"].push(item.path);
    } else if (item.path.includes("裙") || item.path.includes("连衣裙")) {
      recommendations["连体类"].push(item.path);
    } else {
      recommendations["常用服装分类"].push(item.path);
    }
  });

  Object.keys(recommendations).forEach((category) => {
    if (recommendations[category].length > 0) {
      console.log(`${category}:`);
      recommendations[category].forEach((path) => {
        console.log(`  - ${path}`);
      });
      console.log("");
    }
  });

  return recommendations;
}

// 主函数
function main() {
  try {
    console.log("聚水潭分类路径发现工具启动...\n");

    // 1. 加载配置
    const config = loadCategoriesConfig();
    const categories = config.full_categories;

    console.log(`加载了 ${categories.length} 个分类\n`);

    // 2. 构建路径映射
    const pathMap = buildCategoryPaths(categories);

    // 3. 分析服装分类
    const clothingCategories = analyzeClothingCategories(pathMap, categories);

    // 4. 生成推荐路径
    const recommendations = generateRecommendedPaths(clothingCategories);

    // 5. 测试具体路径
    console.log("=== 测试常用分类路径 ===\n");

    const testCategories = ["制衣", "T恤", "上衣", "服装", "连衣裙", "裤子"];
    testCategories.forEach((name) => {
      const path = pathMap.get(name);
      if (path) {
        console.log(`${name} -> ${path}`);
      } else {
        console.log(`${name} -> 未找到`);
      }
    });

    // 6. 保存结果
    const result = {
      total_categories: categories.length,
      clothing_categories: clothingCategories,
      recommendations: recommendations,
      path_mapping: Object.fromEntries(pathMap),
    };

    fs.writeFileSync(
      "./category_paths_analysis.json",
      JSON.stringify(result, null, 2)
    );
    console.log("\n分析结果已保存到: category_paths_analysis.json");
  } catch (error) {
    console.error("分析失败:", error.message);
  }
}

// 运行分析
if (require.main === module) {
  main();
}

module.exports = {
  loadCategoriesConfig,
  buildCategoryPaths,
  analyzeClothingCategories,
  generateRecommendedPaths,
};
