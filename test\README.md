# 🚀 聚水潭ERP集成脚本

## 📋 功能概述

本脚本实现了从飞书外采下单表到聚水潭ERP系统的完整数据同步功能，支持：

- ✅ **飞书数据获取**：直接从飞书多维表获取真实订单数据
- ✅ **状态自动回写**：处理状态和结果自动更新到飞书表格
- ✅ **实时分类更新**：自动获取聚水潭最新商品分类
- ✅ **供应商编码管理**：支持外采款式编码和供应商商品编码
- ✅ **智能分类映射**：自动处理分类格式，确保使用叶子节点
- ✅ **分类格式验证**：检查分类是否为API要求的叶子节点
- ✅ **完整业务流程**：供应商管理、商品创建、采购单生成

## 📁 目录结构

```
test/
├── 核心脚本/
│   └── 聚水潭ERP集成脚本.js           # 🎯 生产环境主脚本
├── 工具脚本/
│   ├── 聚水潭分类获取工具.js           # 📥 获取最新分类配置
│   └── 聚水潭分类格式诊断工具.js        # 🔍 诊断分类格式问题
├── 配置数据/
│   ├── 聚水潭官方分类配置.json         # 📋 分类配置数据
│   ├── 分类诊断结果.json             # 🔍 分类诊断报告
│   ├── 问题修复报告.md               # 📝 问题修复文档
│   └── 飞书集成使用说明.md            # 🔗 飞书集成使用指南
└── README.md                        # 📖 本文档
```

## 🚀 快速开始

### 环境要求
- Node.js >= 14.0
- 网络访问聚水潭API

### 运行脚本

#### 测试模式（使用模拟数据）
```bash
# 进入核心脚本目录
cd 核心脚本

# 运行测试模式
node 聚水潭ERP集成脚本.js test
```

#### 飞书模式（处理真实飞书数据）
```bash
# 处理指定飞书记录
node 聚水潭ERP集成脚本.js feishu <recordId>

# 示例
node 聚水潭ERP集成脚本.js feishu recXXXXXXXXXXXX
```

### 配置设置
编辑脚本中的配置参数：
```javascript
const JUSHUITAN_CONFIG = {
  APP_KEY: "your_app_key",
  APP_SECRET: "your_app_secret", 
  ACCESS_TOKEN: "your_access_token",
  BASE_URL: "https://openapi.jushuitan.com"
};
```

## 📊 数据字段映射

### 外采下单表 → 聚水潭ERP

| 飞书字段 | 聚水潭字段 | 说明 |
|---------|-----------|------|
| 内部款式编码 | i_id, sku_id | 生成内部商品编码 |
| 外部款式编码 | supplier_i_id | 供应商款式编码 |
| 颜色+尺码 | sku_id | 生成完整SKU编码 |
| 采购单价 | s_price | 商品价格 |
| 档口 | supplier_id | 供应商ID |
| 分类 | c_name | 实时校验后的分类名称 |

### 示例数据格式
```javascript
{
  内部款式编码: "AMX0070",        // 必填
  外部款式编码: "B21999",        // 供应商款式编码
  颜色: "白色",                 // 必填
  S: "5", M: "10", L: "8",      // 尺码数量
  采购单价: "89.90",            // 必填
  档口: "测试档口",             // 必填
  分类: "服装-制衣",            // 可选，支持智能映射
  图片: [{ url: "..." }]        // 可选
}
```

## 🔧 核心功能

### 1. 实时分类更新
- 每次运行自动调用聚水潭API获取最新分类
- 智能缓存机制减少重复调用
- 支持降级到本地配置文件

### 2. 供应商编码管理
- **供应商款式编码**：来自外采下单表的"外部款式编码"
- **供应商商品编码**：自动生成格式 `供应商款式编码-颜色-尺码`
- **内部商品编码**：格式 `内部款式编码-颜色-尺码`

### 3. 智能分类映射与验证
- **叶子节点检查**：确保分类符合API要求（必须是叶子节点）
- **智能映射**：`"制衣"` (父节点) → `"T恤"` (叶子节点)
- **分类建议**：为无效分类提供可用的叶子节点建议
- **实时验证**：每次运行时验证分类有效性

### 4. 完整业务流程
1. 处理外采下单数据
2. 实时校验商品分类
3. 验证/创建供应商
4. 生成SKU并创建商品
5. 创建采购单

## 🛠️ 工具脚本

### 获取最新分类配置
```bash
cd 工具脚本
node 聚水潭分类获取工具.js
```
- 功能：从聚水潭API获取最新的商品分类配置
- 输出：更新 `配置数据/聚水潭官方分类配置.json`

### 诊断分类格式问题
```bash
cd 工具脚本
node 聚水潭分类格式诊断工具.js
```
- 功能：诊断分类格式问题，检查叶子节点要求
- 输出：生成 `配置数据/分类诊断结果.json`

## 📈 输出结果

成功执行后返回：
```javascript
{
  "status": "success",
  "data": {
    "purchase_order_id": 405284,      // 采购单ID
    "supplier_id": 30628727,          // 供应商ID
    "processed_skus": 4,              // 处理的SKU数量
    "processing_time": 4.128,         // 处理耗时(秒)
    "internal_style_code": "AMX0070", // 内部款式编码
    "supplier_style_code": "B21999",  // 供应商款式编码
    "category_used": "T恤",           // 最终使用的分类（叶子节点）
    "original_category": "T恤",       // 原始输入分类
    "category_validation": {          // 分类验证结果
      "valid": true,
      "message": "分类名称\"T恤\"有效且为叶子节点"
    }
  }
}
```

## 🔧 分类格式问题解决

### 常见问题
**错误**: `分类【制衣】不存在，请修改分类或者重新维护【商品类目管理】`

### 解决方案
1. **问题原因**: 聚水潭API要求使用叶子节点分类，"制衣"是父节点
2. **自动修复**: 脚本会自动将父节点转换为叶子节点
3. **推荐分类**: T恤、衬衫、短外套等叶子节点分类

### 可用的叶子节点分类
```
服装类: 基础T恤, 时尚T恤, 衬衫, 短外套, 风衣, 羽绒服, 棉衣/棉服
下装类: 休闲裤, 短裤, 牛仔裤, 打底裤, 西装裤/正装裤
裙装类: 半身裙, 连衣裙
其他类: 背心吊带, 卫衣/绒衫, 套头衫, 马甲, 时装上衣
```

## 🔗 飞书集成功能

### 功能特性
- ✅ **自动数据获取**：从飞书多维表直接获取订单数据
- ✅ **状态实时回写**：处理状态自动更新到飞书表格
- ✅ **错误信息记录**：失败原因详细记录到飞书
- ✅ **采购单号回写**：成功创建的采购单号自动填入飞书

### 飞书字段映射
```
输入字段（从飞书读取）:
内部款式编码, 外部款式编码, 颜色, S, M, L, 均码（F）
采购单价, 采购日期, 采购人员, 档口, 图片, 备注, 成分

输出字段（回写到飞书）:
处理状态, 采购单号, 处理时间, 错误信息
```

### 使用示例
```bash
# 处理飞书记录ID为 recXXXXXXXXXXXX 的数据
node 聚水潭ERP集成脚本.js feishu recXXXXXXXXXXXX
```

详细使用说明请参考：`配置数据/飞书集成使用说明.md`

## ⚠️ 注意事项

1. **API频率限制**：脚本包含重试机制和延迟控制
2. **数据要求**：必需字段为内部款式编码、颜色、尺码数量、档口
3. **分类处理**：自动检查并使用叶子节点分类，避免API错误
4. **错误处理**：完整的错误日志和异常处理机制

## 📞 技术支持

如有问题，请检查：
1. API密钥配置是否正确
2. 网络是否能访问聚水潭API
3. 输入数据格式是否符合要求
4. 查看详细的控制台日志输出

---

**🎯 本脚本为生产就绪版本，可直接用于飞书外采同步聚水潭ERP系统的实际业务场景。** 