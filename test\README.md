# 🚀 聚水潭ERP集成脚本

## 📋 功能概述

本脚本实现了从飞书外采下单表到聚水潭ERP系统的完整数据同步功能，支持：

- ✅ **实时分类更新**：自动获取聚水潭最新商品分类
- ✅ **供应商编码管理**：支持外采款式编码和供应商商品编码
- ✅ **智能分类映射**：自动处理分类路径到叶节点的转换
- ✅ **完整业务流程**：供应商管理、商品创建、采购单生成

## 📁 目录结构

```
test/
├── 核心脚本/
│   └── 聚水潭ERP集成脚本.js      # 生产环境主脚本
├── 工具脚本/
│   ├── 聚水潭分类获取工具.js      # 获取最新分类配置
│   └── 聚水潭分类路径分析工具.js   # 分析分类层级结构
├── 配置数据/
│   ├── 聚水潭官方分类配置.json    # 分类配置数据
│   └── 分类路径分析结果.json     # 分类路径分析结果
└── README.md                   # 本文档
```

## 🚀 快速开始

### 环境要求
- Node.js >= 14.0
- 网络访问聚水潭API

### 运行脚本
```bash
# 进入核心脚本目录
cd 核心脚本

# 运行主脚本
node 聚水潭ERP集成脚本.js
```

### 配置设置
编辑脚本中的配置参数：
```javascript
const JUSHUITAN_CONFIG = {
  APP_KEY: "your_app_key",
  APP_SECRET: "your_app_secret", 
  ACCESS_TOKEN: "your_access_token",
  BASE_URL: "https://openapi.jushuitan.com"
};
```

## 📊 数据字段映射

### 外采下单表 → 聚水潭ERP

| 飞书字段 | 聚水潭字段 | 说明 |
|---------|-----------|------|
| 内部款式编码 | i_id, sku_id | 生成内部商品编码 |
| 外部款式编码 | supplier_i_id | 供应商款式编码 |
| 颜色+尺码 | sku_id | 生成完整SKU编码 |
| 采购单价 | s_price | 商品价格 |
| 档口 | supplier_id | 供应商ID |
| 分类 | c_name | 实时校验后的分类名称 |

### 示例数据格式
```javascript
{
  内部款式编码: "AMX0070",        // 必填
  外部款式编码: "B21999",        // 供应商款式编码
  颜色: "白色",                 // 必填
  S: "5", M: "10", L: "8",      // 尺码数量
  采购单价: "89.90",            // 必填
  档口: "测试档口",             // 必填
  分类: "服装-制衣",            // 可选，支持智能映射
  图片: [{ url: "..." }]        // 可选
}
```

## 🔧 核心功能

### 1. 实时分类更新
- 每次运行自动调用聚水潭API获取最新分类
- 智能缓存机制减少重复调用
- 支持降级到本地配置文件

### 2. 供应商编码管理
- **供应商款式编码**：来自外采下单表的"外部款式编码"
- **供应商商品编码**：自动生成格式 `供应商款式编码-颜色-尺码`
- **内部商品编码**：格式 `内部款式编码-颜色-尺码`

### 3. 智能分类映射
- 输入：`"服装-制衣"` (分类路径)
- 输出：`"制衣"` (API可用的叶节点分类)
- 自动建议相似分类

### 4. 完整业务流程
1. 处理外采下单数据
2. 实时校验商品分类
3. 验证/创建供应商
4. 生成SKU并创建商品
5. 创建采购单

## 🛠️ 工具脚本

### 更新分类配置
```bash
cd 工具脚本
node 聚水潭分类获取工具.js
```

### 分析分类结构
```bash
cd 工具脚本
node 聚水潭分类路径分析工具.js
```

## 📈 输出结果

成功执行后返回：
```javascript
{
  "status": "success",
  "data": {
    "purchase_order_id": 405284,      // 采购单ID
    "supplier_id": 30628727,          // 供应商ID
    "processed_skus": 4,              // 处理的SKU数量
    "processing_time": 4.128,         // 处理耗时(秒)
    "internal_style_code": "AMX0070", // 内部款式编码
    "supplier_style_code": "B21999",  // 供应商款式编码
    "category_used": "制衣",          // 最终使用的分类
    "original_category": "服装-制衣"   // 原始输入分类
  }
}
```

## ⚠️ 注意事项

1. **API频率限制**：脚本包含重试机制和延迟控制
2. **数据要求**：必需字段为内部款式编码、颜色、尺码数量、档口
3. **分类处理**：支持分类路径自动转换为叶节点分类
4. **错误处理**：完整的错误日志和异常处理机制

## 📞 技术支持

如有问题，请检查：
1. API密钥配置是否正确
2. 网络是否能访问聚水潭API
3. 输入数据格式是否符合要求
4. 查看详细的控制台日志输出

---

**🎯 本脚本为生产就绪版本，可直接用于飞书外采同步聚水潭ERP系统的实际业务场景。** 