---
alwaysApply: true
description: 飞书外采同步聚水潭ERP系统项目结构和架构指南
---

# 🚀 飞书外采同步聚水潭ERP系统 - 项目结构指南

## 📁 核心目录结构

本项目基于**云端无服务器架构**，主要组件如下：

### ⭐ 核心脚本目录
- `wps_airscript/` - WPS AirScript云端脚本
  - [飞书一键创建聚水潭采购单(WPS AirScript 一体化版本).js](mdc:wps_airscript/飞书一键创建聚水潭采购单(WPS AirScript 一体化版本).js) - **核心业务逻辑**

### 📚 技术文档目录  
- `docs/` - 完整技术文档
  - [聚水潭API集成细节说明.md](mdc:docs/聚水潭API集成细节说明.md) - API集成规范
  - [飞书Webhook触发WPS_AirScript一键创建采购单方案.md](mdc:docs/飞书Webhook触发WPS_AirScript一键创建采购单方案.md) - 核心技术方案
  - `jushuitan_api/` - 聚水潭API官方文档

### 📊 数据分析目录
- `reports/` - 业务分析报告
  - [外采管理表关系分析.md](mdc:reports/外采管理表关系分析.md) - 业务表结构分析
  - [AHMI运营协助更进表_完整分析报告_20250725_115902.md](mdc:reports/AHMI运营协助更进表_完整分析报告_20250725_115902.md) - **完整业务环境分析** (48个表格全貌)
- `tools/` - 分析工具
  - [analyze_feishu_base.py](mdc:tools/analyze_feishu_base.py) - 飞书表结构分析工具

### 🧪 测试数据目录
- `demo_data/` - 测试用例和示例数据
  - [test_feishu_data.json](mdc:demo_data/test_feishu_data.json) - 标准测试数据

## 🎯 技术架构

**触发流程**: 飞书多维表 → Webhook → WPS AirScript → 聚水潭ERP API

**核心价值**: 零服务器部署，一键式操作，实时状态反馈
