/**
 * 外采清单表结构查看工具
 *
 * 模块名称：外采清单表结构查看工具
 * 模块描述：专门查看外采清单表的字段结构和数据
 * 模块职责：字段分析、数据类型确认、记录查看
 * 修改时间: 2025-07-26 17:15
 */

const https = require("https");

//===================================================================================
// 📋 配置区域
//===================================================================================

const FEISHU_CONFIG = {
  APP_ID: "cli_a73da8fdc6fe900d",
  APP_SECRET: "CM0Vl3kDoKUHRim3JuJznXT1rqTBbrQa",
  BASE_URL: "https://open.feishu.cn/open-apis",
  BASE_ID: "E1Q3bDq3harjR1s8qr3cyKz6n1b",
  PURCHASE_TABLE_ID: "tblCgMR5F7T3gicd", // 外采清单表
};

//===================================================================================
// 🔧 工具函数
//===================================================================================

function log(level, message) {
  const timestamp = new Date().toISOString();
  console.log(`${timestamp} [${level}] ${message}`);
}

async function makeHttpRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname + urlObj.search,
      method: options.method || "GET",
      headers: options.headers || {},
      timeout: options.timeout || 30000,
    };

    const req = https.request(requestOptions, (res) => {
      let data = "";
      res.on("data", (chunk) => { data += chunk; });
      res.on("end", () => {
        try {
          resolve(JSON.parse(data));
        } catch (error) {
          reject(new Error(`JSON解析失败: ${error.message}`));
        }
      });
    });

    req.on("error", reject);
    req.on("timeout", () => {
      req.destroy();
      reject(new Error("请求超时"));
    });

    if (options.body) {
      req.write(options.body);
    }
    req.end();
  });
}

//===================================================================================
// 🔗 飞书API函数
//===================================================================================

async function getFeishuAccessToken() {
  try {
    const url = `${FEISHU_CONFIG.BASE_URL}/auth/v3/tenant_access_token/internal`;
    const data = JSON.stringify({
      app_id: FEISHU_CONFIG.APP_ID,
      app_secret: FEISHU_CONFIG.APP_SECRET,
    });

    const response = await makeHttpRequest(url, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: data,
    });

    if (response && response.code === 0) {
      return response.tenant_access_token;
    }
    throw new Error(`获取token失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `飞书token获取失败: ${error.message}`);
    return null;
  }
}

async function getPurchaseTableFields() {
  try {
    log("INFO", "获取外采清单表字段结构...");
    
    const token = await getFeishuAccessToken();
    if (!token) throw new Error("获取飞书token失败");

    const url = `${FEISHU_CONFIG.BASE_URL}/bitable/v1/apps/${FEISHU_CONFIG.BASE_ID}/tables/${FEISHU_CONFIG.PURCHASE_TABLE_ID}/fields`;
    
    const response = await makeHttpRequest(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    if (response && response.code === 0) {
      return response.data.items;
    }
    throw new Error(`获取字段失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `字段获取失败: ${error.message}`);
    return null;
  }
}

async function getPurchaseTableRecords() {
  try {
    log("INFO", "获取外采清单表记录...");
    
    const token = await getFeishuAccessToken();
    if (!token) throw new Error("获取飞书token失败");

    const url = `${FEISHU_CONFIG.BASE_URL}/bitable/v1/apps/${FEISHU_CONFIG.BASE_ID}/tables/${FEISHU_CONFIG.PURCHASE_TABLE_ID}/records?page_size=5`;
    
    const response = await makeHttpRequest(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    if (response && response.code === 0) {
      return response.data.items;
    }
    throw new Error(`获取记录失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `记录获取失败: ${error.message}`);
    return null;
  }
}

//===================================================================================
// 🧪 分析函数
//===================================================================================

async function analyzePurchaseTable() {
  try {
    log("INFO", "=== 开始分析外采清单表 ===");
    
    // 1. 获取字段结构
    const fields = await getPurchaseTableFields();
    if (!fields) {
      throw new Error("无法获取字段结构");
    }
    
    log("INFO", `外采清单表包含 ${fields.length} 个字段:`);
    
    const fieldTypes = {
      1: "文本",
      2: "数字", 
      3: "单选",
      4: "多选",
      5: "日期",
      7: "复选框",
      11: "人员",
      17: "附件",
      18: "单向关联",
      21: "双向关联"
    };
    
    fields.forEach((field, index) => {
      const typeName = fieldTypes[field.type] || `未知类型(${field.type})`;
      log("INFO", `  ${index + 1}. ${field.field_name} - ${typeName} (${field.field_id})`);
    });
    
    // 2. 获取现有记录
    const records = await getPurchaseTableRecords();
    if (records && records.length > 0) {
      log("INFO", `\n外采清单表包含 ${records.length} 条记录:`);
      
      records.forEach((record, index) => {
        log("INFO", `\n记录 ${index + 1} (${record.record_id}):`);
        Object.entries(record.fields).forEach(([fieldName, fieldValue]) => {
          log("INFO", `  ${fieldName}: ${JSON.stringify(fieldValue)}`);
        });
      });
    } else {
      log("INFO", "\n外采清单表暂无记录");
    }
    
    // 3. 输出详细分析
    console.log("\n=== 详细字段分析 ===");
    console.log(JSON.stringify(fields, null, 2));
    
    if (records) {
      console.log("\n=== 详细记录分析 ===");
      console.log(JSON.stringify(records, null, 2));
    }
    
    return { fields, records };
  } catch (error) {
    log("ERROR", `分析失败: ${error.message}`);
    return null;
  }
}

//===================================================================================
// 🚀 主函数
//===================================================================================

async function main() {
  try {
    const analysis = await analyzePurchaseTable();
    
    if (analysis) {
      log("INFO", "=== 外采清单表分析完成 ===");
      
      // 检查关键字段
      const keyFields = ["采购单", "采购单日期", "采购人"];
      const { fields } = analysis;
      
      log("INFO", "\n=== 关键字段检查 ===");
      keyFields.forEach(fieldName => {
        const field = fields.find(f => f.field_name === fieldName);
        if (field) {
          const fieldTypes = {
            1: "文本", 2: "数字", 3: "单选", 4: "多选", 5: "日期",
            7: "复选框", 11: "人员", 17: "附件", 18: "单向关联", 21: "双向关联"
          };
          const typeName = fieldTypes[field.type] || `未知类型(${field.type})`;
          log("INFO", `✅ ${fieldName}: ${typeName} (${field.field_id})`);
        } else {
          log("WARN", `❌ ${fieldName}: 字段不存在`);
        }
      });
    }
  } catch (error) {
    log("ERROR", `主函数执行失败: ${error.message}`);
  }
}

// 执行分析
if (require.main === module) {
  main();
}
