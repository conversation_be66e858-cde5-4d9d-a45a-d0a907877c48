/**
 * 飞书数据插入测试工具
 *
 * 模块名称：飞书数据插入测试工具
 * 模块描述：向飞书多维表插入测试数据，用于测试聚水潭ERP集成脚本
 * 模块职责：飞书API调用、测试数据生成、记录插入、记录ID获取
 * 修改时间: 2025-07-26 14:58
 */

const https = require("https");

//------------------
// 配置区域
//------------------

// 飞书API配置
const FEISHU_CONFIG = {
  APP_ID: "cli_a73da8fdc6fe900d",
  APP_SECRET: "CM0Vl3kDoKUHRim3JuJznXT1rqTBbrQa",
  BASE_URL: "https://open.feishu.cn/open-apis",

  // 多维表信息
  BASE_ID: "E1Q3bDq3harjR1s8qr3cyKz6n1b",
  TABLE_ID: "tblwJFuLDpV62Z9p",

  // API超时设置
  API_TIMEOUT: 30000,
};

// 测试数据模板（确保数据类型正确）
const TEST_DATA_TEMPLATE = {
  内部款式编码: "TEST002",
  外部款式编码: "D00002",
  颜色: "蓝色",
  S: "12", // 改为字符串类型
  M: "18", // 改为字符串类型
  L: "10", // 改为字符串类型
  "均码（F）": "0", // 改为字符串类型
  采购单价: "52.80", // 改为字符串类型
  采购日期: new Date().toISOString().split("T")[0],
  采购人员: "测试人员",
  档口: "测试档口-飞书集成",
  图片: "",
  备注: "飞书集成测试数据",
  成分: "100%棉",
  处理状态: "待处理",
  采购单号: "",
  处理时间: "",
  错误信息: "",
};

//------------------
// 工具函数
//------------------

/**
 * 日志输出函数
 * @param {string} level - 日志级别
 * @param {string} message - 日志消息
 */
function log(level, message) {
  const timestamp = new Date().toISOString();
  console.log(`${timestamp} [${level}] ${message}`);
}

/**
 * 通用HTTP请求函数
 * @param {string} url - 请求URL
 * @param {Object} options - 请求选项
 * @returns {Promise<Object>} 响应数据
 */
async function makeHttpRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === "https:";
    const httpModule = isHttps ? require("https") : require("http");

    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || "GET",
      headers: options.headers || {},
      timeout: options.timeout || 30000,
    };

    const req = httpModule.request(requestOptions, (res) => {
      let data = "";

      res.on("data", (chunk) => {
        data += chunk;
      });

      res.on("end", () => {
        try {
          const jsonData = JSON.parse(data);
          resolve(jsonData);
        } catch (error) {
          reject(new Error(`JSON解析失败: ${error.message}`));
        }
      });
    });

    req.on("error", (error) => {
      reject(new Error(`请求失败: ${error.message}`));
    });

    req.on("timeout", () => {
      req.destroy();
      reject(new Error("请求超时"));
    });

    if (options.body) {
      req.write(options.body);
    }

    req.end();
  });
}

//------------------
// 飞书API函数
//------------------

/**
 * 获取飞书访问令牌
 * @returns {Promise<string|null>} 访问令牌
 */
async function getFeishuAccessToken() {
  try {
    const url = `${FEISHU_CONFIG.BASE_URL}/auth/v3/tenant_access_token/internal`;
    const data = JSON.stringify({
      app_id: FEISHU_CONFIG.APP_ID,
      app_secret: FEISHU_CONFIG.APP_SECRET,
    });

    const response = await makeHttpRequest(url, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: data,
      timeout: FEISHU_CONFIG.API_TIMEOUT,
    });

    if (response && response.code === 0) {
      log("DEBUG", "飞书token获取成功");
      return response.tenant_access_token;
    }

    throw new Error(`获取token失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `飞书token获取失败: ${error.message}`);
    return null;
  }
}

/**
 * 向飞书多维表插入记录
 * @param {string} baseId - 多维表BaseID
 * @param {string} tableId - 表格ID
 * @param {Object} recordData - 记录数据
 * @returns {Promise<string|null>} 记录ID
 */
async function insertFeishuRecord(baseId, tableId, recordData) {
  try {
    const token = await getFeishuAccessToken();
    if (!token) {
      throw new Error("获取飞书token失败");
    }

    const insertData = {
      fields: recordData,
    };

    const url = `${FEISHU_CONFIG.BASE_URL}/bitable/v1/apps/${baseId}/tables/${tableId}/records`;

    const response = await makeHttpRequest(url, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(insertData),
      timeout: FEISHU_CONFIG.API_TIMEOUT,
    });

    if (response && response.code === 0) {
      const recordId = response.data.record.record_id;
      log("INFO", `飞书记录插入成功: ${recordId}`);
      return recordId;
    }

    throw new Error(`插入记录失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `飞书记录插入失败: ${error.message}`);
    return null;
  }
}

//------------------
// 主要功能函数
//------------------

/**
 * 生成测试数据
 * @returns {Object} 测试数据
 */
function generateTestData() {
  const timestamp = new Date().toISOString().replace(/[:.]/g, "").slice(0, 15);
  const testData = {
    ...TEST_DATA_TEMPLATE,
    内部款式编码: `TEST${timestamp.slice(-6)}`,
    外部款式编码: `D${timestamp.slice(-5)}`,
    备注: `飞书集成测试数据 - ${new Date().toLocaleString("zh-CN")}`,
  };

  log("INFO", `生成测试数据: ${testData.内部款式编码}`);
  return testData;
}

/**
 * 执行完整的测试流程
 * @returns {Promise<Object>} 测试结果
 */
async function runTestFlow() {
  log("INFO", "=== 开始飞书数据插入测试 ===");

  try {
    // 1. 生成测试数据
    log("INFO", "第1步: 生成测试数据");
    const testData = generateTestData();

    log("INFO", "测试数据详情:");
    Object.entries(testData).forEach(([key, value]) => {
      log("INFO", `  ${key}: ${value}`);
    });

    // 2. 插入飞书记录
    log("INFO", "第2步: 插入飞书记录");
    const recordId = await insertFeishuRecord(
      FEISHU_CONFIG.BASE_ID,
      FEISHU_CONFIG.TABLE_ID,
      testData
    );

    if (!recordId) {
      throw new Error("记录插入失败");
    }

    // 3. 返回测试结果
    const result = {
      status: "success",
      message: "飞书测试数据插入成功",
      data: {
        record_id: recordId,
        base_id: FEISHU_CONFIG.BASE_ID,
        table_id: FEISHU_CONFIG.TABLE_ID,
        test_data: testData,
        next_step: `运行命令: node "test\\核心脚本\\聚水潭ERP集成脚本.js" feishu ${recordId}`,
      },
    };

    log("INFO", "=== 飞书数据插入测试完成 ===");
    log("INFO", `记录ID: ${recordId}`);
    log(
      "INFO",
      `下一步: node "test\\核心脚本\\聚水潭ERP集成脚本.js" feishu ${recordId}`
    );

    return result;
  } catch (error) {
    log("ERROR", `=== 飞书数据插入测试失败 ===`);
    log("ERROR", `错误信息: ${error.message}`);

    return {
      status: "error",
      message: error.message,
    };
  }
}

//------------------
// 执行入口
//------------------

if (require.main === module) {
  runTestFlow()
    .then((result) => {
      console.log("=== 最终结果 ===");
      console.log(JSON.stringify(result, null, 2));
      process.exit(result.status === "success" ? 0 : 1);
    })
    .catch((error) => {
      console.error("执行错误:", error);
      process.exit(1);
    });
}

module.exports = {
  runTestFlow,
  insertFeishuRecord,
  generateTestData,
  getFeishuAccessToken,
  FEISHU_CONFIG,
};
