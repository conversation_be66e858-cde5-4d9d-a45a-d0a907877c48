/**
 * 聚水潭API增强版本 - 带实时分类更新、供应商编码和飞书数据获取
 *
 * 模块名称：聚水潭API增强版本(实时分类+供应商编码+飞书集成)
 * 模块描述：集成飞书数据获取、实时分类校验、供应商编码生成的完整解决方案
 * 模块职责：飞书数据获取、实时分类更新、数据处理、SKU生成、ERP API调用、供应商编码管理
 * 修改时间: 2025-07-26 14:44
 *
 * 新增功能：
 * 1. 飞书多维表数据获取和状态回写
 * 2. 实时获取聚水潭最新分类配置
 * 3. 支持供应商款式编码(外采款式编码)
 * 4. 自动生成供应商商品编码
 * 5. 增强的商品创建参数
 * 6. 叶子节点分类验证
 */

//===================================================================================
// 📋 配置参数区
//===================================================================================

// 聚水潭ERP API配置
const JUSHUITAN_CONFIG = {
  APP_KEY: "13a2701994bd4230a1ed9a12302ba30a",
  APP_SECRET: "f6e5dd9d168d4817973cb42f121218a0",
  ACCESS_TOKEN: "9a9d33072cc8450b916b7c8dd830d22c",
  BASE_URL: "https://openapi.jushuitan.com",

  // 业务默认配置
  DEFAULT_SUPPLIER_ID: 1,
  DEFAULT_WAREHOUSE_ID: 1,
  DEFAULT_WMS_CO_ID: 0,

  // API调用控制
  API_TIMEOUT: 30000,
  RETRY_TIMES: 5,
  RETRY_DELAY: 1000,
};

// 飞书API配置
const FEISHU_CONFIG = {
  APP_ID: "cli_a73da8fdc6fe900d",
  APP_SECRET: "CM0Vl3kDoKUHRim3JuJznXT1rqTBbrQa",
  BASE_URL: "https://open.feishu.cn/open-apis",

  // 多维表信息
  BASE_ID: "E1Q3bDq3harjR1s8qr3cyKz6n1b",
  TABLE_ID: "tblwJFuLDpV62Z9p",
  VIEW_ID: "vewoihqbAb",

  // API超时设置
  API_TIMEOUT: 30000,
  RETRY_TIMES: 3,
  RETRY_DELAY: 1000,
};

// 业务配置
const BUSINESS_CONFIG = {
  // 支持的尺码映射
  SIZE_MAPPING: {
    S: "S",
    M: "M",
    L: "L",
    "均码（F）": "F",
    XS: "XS",
    XL: "XL",
    XXL: "XXL",
  },

  // 飞书字段映射
  FIELD_MAPPING: {
    内部款式编码: "style_code",
    外部款式编码: "external_style_code",
    颜色: "color",
    S: "size_s_qty",
    M: "size_m_qty",
    L: "size_l_qty",
    "均码（F）": "size_f_qty",
    采购单价: "unit_price",
    采购日期: "purchase_date",
    采购人员: "purchaser",
    档口: "supplier_stall",
    图片: "product_images",
    备注: "remark",
    成分: "material",
    处理状态: "status",
    采购单号: "purchase_order_id",
    处理时间: "process_time",
    错误信息: "error_message",
  },

  // 状态枚举（增强版：双状态字段）
  STATUS_ENUM: {
    PENDING: "待处理",
    PROCESSING: "处理中",
    COMPLETED: "已完成",
    FAILED: "失败",
  },

  // 飞书状态映射配置（基于实际表格结构）
  FEISHU_STATUS_MAPPING: {
    // 初始状态
    INITIAL: {
      流程状态: "未下单",
    },
    // 处理中
    PROCESSING: {
      流程状态: "未下单",
    },
    // 采购单创建成功
    ORDER_CREATED: {
      流程状态: "在途中",
    },
    // 完成
    COMPLETED: {
      流程状态: "已全部到货",
    },
    // 失败
    FAILED: {
      流程状态: "未下单",
    },
  },

  // 飞书字段ID映射（基于实际表格结构）
  FEISHU_FIELD_IDS: {
    内部款式编码: "fldZHToxxr",
    外部款式编码: "fld3Jb4RsT",
    颜色: "fldSUw8Brj",
    采购单价: "fldcqZUSaC",
    档口: "fldhhC9rk6",
    流程状态: "fldhRgjsvB",
    采购单: "fldXJbLCog",
    S: "fld1zcTMEr",
    M: "fldqWQUXe1",
    L: "fldr4wClwu",
    "均码（F）": "fld8uhdwkI",
    备注: "fldrxIOdSs",
    成分: "fldMVdkoaY",
  },
};

// 测试数据配置 (外采下单表数据结构)
const TEST_DATA = {
  // 模拟外采下单表数据
  feishuData: {
    内部款式编码: "TEST001", // 内部款式编码
    外部款式编码: "D00001", // 外采款式编码 (供应商款式编码)
    颜色: "白色",
    S: "5",
    M: "10",
    L: "8",
    "均码（F）": "3",
    采购单价: "89.90",
    档口: "测试档口003",
    分类: "基础T恤", // 使用有效的叶子节点分类（修复：T恤也是父节点，必须使用叶子节点）
    图片: [{ url: "https://example.com/test-product.jpg" }],
    备注: "实时分类更新+供应商编码测试数据(修复版)",
  },
};

// 全局分类配置缓存
let CATEGORIES_CONFIG = null;

//===================================================================================
// 🛠️ 工具函数区
//===================================================================================

/**
 * 简单的控制台日志函数
 * @param {string} level - 日志级别
 * @param {...any} messages - 消息内容
 */
function log(level, ...messages) {
  const timestamp = new Date().toISOString();
  const logMessage = `${timestamp} [${level.toUpperCase()}] ${messages.join(
    " "
  )}`;

  switch (level.toUpperCase()) {
    case "INFO":
      console.info(logMessage);
      break;
    case "ERROR":
      console.error(logMessage);
      break;
    case "WARN":
      console.warn(logMessage);
      break;
    case "DEBUG":
      console.log(logMessage);
      break;
    default:
      console.log(logMessage);
  }
}

/**
 * 计算MD5哈希值
 * @param {string} str - 待计算的字符串
 * @returns {string} MD5哈希值
 */
async function calculateMD5(str) {
  if (typeof require !== "undefined") {
    const crypto = require("crypto");
    const https = require("https");
    return crypto.createHash("md5").update(str).digest("hex");
  }
  throw new Error("浏览器环境暂不支持MD5计算");
}

/**
 * 延迟函数
 * @param {number} ms - 延迟毫秒数
 * @returns {Promise}
 */
function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

//===================================================================================
// 🔧 API调用基础函数
//===================================================================================

/**
 * 构建聚水潭API参数
 * @param {Object} bizParams - 业务参数
 * @returns {Object} 完整的API参数对象
 */
function buildJushuitanParams(bizParams) {
  const params = {
    access_token: JUSHUITAN_CONFIG.ACCESS_TOKEN,
    app_key: JUSHUITAN_CONFIG.APP_KEY,
    charset: "utf-8",
    timestamp: Math.floor(Date.now() / 1000),
    version: "2",
    biz: JSON.stringify(bizParams),
  };

  // 按ASCII码排序
  const sortedParams = {};
  const keys = Object.keys(params).sort();
  keys.forEach((key) => {
    sortedParams[key] = params[key];
  });

  return sortedParams;
}

/**
 * 生成聚水潭API签名
 * @param {Object} params - API参数对象
 * @returns {Promise<string>} 生成的签名字符串
 */
async function generateJushuitanSignature(params) {
  try {
    const filteredKeys = Object.keys(params)
      .filter((key) => key !== "sign")
      .sort();

    const signParts = filteredKeys.map((key) => key + params[key]);
    const signString = JUSHUITAN_CONFIG.APP_SECRET + signParts.join("");

    return await calculateMD5(signString);
  } catch (error) {
    log("ERROR", `签名生成失败: ${error.message}`);
    return null;
  }
}

/**
 * 调用聚水潭API的通用函数
 * @param {string} apiPath - API路径
 * @param {Object} bizParams - 业务参数
 * @param {string} apiName - API名称(用于日志)
 * @returns {Promise<Object|null>} API响应结果的data部分
 */
async function callJushuitanAPI(apiPath, bizParams, apiName) {
  const maxRetries = JUSHUITAN_CONFIG.RETRY_TIMES;
  const apiUrl = `${JUSHUITAN_CONFIG.BASE_URL}${apiPath}`;

  try {
    log("INFO", `调用聚水潭API: ${apiName}`);
    log("DEBUG", `API路径: ${apiPath}`);

    // 构建参数和签名
    const params = buildJushuitanParams(bizParams);
    const sign = await generateJushuitanSignature(params);
    if (!sign) {
      throw new Error("签名生成失败");
    }
    params.sign = sign;

    // 构建请求体
    const formData = new URLSearchParams();
    Object.keys(params).forEach((key) => {
      formData.append(key, params[key]);
    });

    // 重试机制
    for (let retryCount = 0; retryCount < maxRetries; retryCount++) {
      try {
        log("INFO", `正在请求 ${apiName} (第${retryCount + 1}次尝试)...`);

        const controller = new AbortController();
        const timeoutId = setTimeout(
          () => controller.abort(),
          JUSHUITAN_CONFIG.API_TIMEOUT
        );

        const response = await fetch(apiUrl, {
          method: "POST",
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
          body: formData,
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          const responseText = await response.text();
          log("DEBUG", `响应内容: ${responseText}`);

          const result = JSON.parse(responseText);

          if (result.code === 0) {
            log("INFO", `${apiName} 请求成功`);
            return result.data;
          } else if (result.code === 199 || result.code === 200) {
            log(
              "WARN",
              `${apiName} 频率限制: ${result.msg}，等待重试...（第${
                retryCount + 1
              }次）`
            );
            await delay(JUSHUITAN_CONFIG.RETRY_DELAY * (retryCount + 1));
            continue;
          } else {
            log(
              "WARN",
              `${apiName} 错误响应: ${JSON.stringify(result)}，等待重试...（第${
                retryCount + 1
              }次）`
            );
            await delay(JUSHUITAN_CONFIG.RETRY_DELAY);
            continue;
          }
        } else {
          throw new Error(`HTTP请求失败: ${response.status}`);
        }
      } catch (httpError) {
        log(
          "WARN",
          `${apiName} 请求异常: ${httpError.message}，等待重试...（第${
            retryCount + 1
          }次）`
        );
        if (retryCount === maxRetries - 1) {
          throw httpError;
        }
        await delay(JUSHUITAN_CONFIG.RETRY_DELAY * (retryCount + 1));
        continue;
      }
    }

    log("ERROR", `${apiName} 重试 ${maxRetries} 次失败`);
    return null;
  } catch (error) {
    log("ERROR", `聚水潭API调用失败 [${apiName}]: ${error.message}`);
    return null;
  }
}

//===================================================================================
// 🔧 飞书字段解析工具
//===================================================================================

/**
 * 解析飞书字段值
 *
 * 概述: 正确解析飞书字段的各种数据类型
 * 详细描述: 处理文本、数字、选项、附件等不同类型的字段
 * 调用的函数: 无
 * 参数:
 *   fieldValue (any): 飞书字段原始值
 *   fieldType (string): 字段类型
 * 返回值: any - 解析后的值
 * 修改时间: 2025-07-26 15:24
 */
function parseFeishuFieldValue(fieldValue, fieldType = "auto") {
  if (fieldValue === null || fieldValue === undefined) {
    return "";
  }

  // 如果是简单类型，直接返回
  if (typeof fieldValue === "string" || typeof fieldValue === "number") {
    return fieldValue;
  }

  // 如果是数组类型（多选、附件等）
  if (Array.isArray(fieldValue)) {
    if (fieldValue.length === 0) return "";

    // 处理附件类型
    if (fieldValue[0] && fieldValue[0].file_token) {
      return fieldValue.map((item) => item.name || item.file_token).join(", ");
    }

    // 处理选项类型
    if (fieldValue[0] && fieldValue[0].text) {
      return fieldValue.map((item) => item.text).join(", ");
    }

    // 处理其他数组类型
    return fieldValue.join(", ");
  }

  // 如果是对象类型
  if (typeof fieldValue === "object") {
    // 处理选项类型
    if (fieldValue.text) {
      return fieldValue.text;
    }

    // 处理人员类型
    if (fieldValue.name) {
      return fieldValue.name;
    }

    // 处理日期类型
    if (fieldValue.date) {
      return fieldValue.date;
    }

    // 处理富文本类型
    if (fieldValue.content) {
      return fieldValue.content;
    }

    // 其他对象类型，尝试JSON序列化
    try {
      return JSON.stringify(fieldValue);
    } catch (error) {
      return String(fieldValue);
    }
  }

  return String(fieldValue);
}

//===================================================================================
// � 飞书API调用区
//===================================================================================

/**
 * 获取飞书访问令牌
 *
 * 概述: 获取飞书API访问令牌
 * 详细描述: 使用应用凭证获取tenant_access_token
 * 调用的函数: 无
 * 返回值: String - 访问令牌
 * 修改时间: 2025-07-26 14:44
 */
async function getFeishuAccessToken() {
  try {
    const url = `${FEISHU_CONFIG.BASE_URL}/auth/v3/tenant_access_token/internal`;
    const data = JSON.stringify({
      app_id: FEISHU_CONFIG.APP_ID,
      app_secret: FEISHU_CONFIG.APP_SECRET,
    });

    const response = await makeHttpRequest(url, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: data,
      timeout: FEISHU_CONFIG.API_TIMEOUT,
    });

    if (response && response.code === 0) {
      log("DEBUG", "飞书token获取成功");
      return response.tenant_access_token;
    }

    throw new Error(`获取token失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `飞书token获取失败: ${error.message}`);
    return null;
  }
}

/**
 * 获取飞书多维表记录
 *
 * 概述: 从飞书多维表获取指定记录数据
 * 详细描述: 根据记录ID获取完整的记录数据，包括所有字段
 * 调用的函数:
 *   - getFeishuAccessToken() 获取访问令牌
 *   - makeHttpRequest() 发送HTTP请求
 * 参数:
 *   baseId (String): 多维表BaseID
 *   tableId (String): 表格ID
 *   recordId (String): 记录ID
 * 返回值: Object - 记录数据
 * 修改时间: 2025-07-26 14:44
 */
async function getFeishuRecord(baseId, tableId, recordId) {
  try {
    const token = await getFeishuAccessToken();
    if (!token) {
      throw new Error("获取飞书token失败");
    }

    const url = `${FEISHU_CONFIG.BASE_URL}/bitable/v1/apps/${baseId}/tables/${tableId}/records/${recordId}`;

    const response = await makeHttpRequest(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      timeout: FEISHU_CONFIG.API_TIMEOUT,
    });

    if (response && response.code === 0) {
      log("INFO", `飞书记录获取成功: ${recordId}`);
      return response.data.record;
    }

    throw new Error(`获取记录失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `飞书记录获取失败: ${error.message}`);
    return null;
  }
}

/**
 * 更新飞书记录状态（增强版：支持双状态字段）
 *
 * 概述: 更新飞书表格记录的状态信息
 * 详细描述: 同时更新处理标签、流程状态、采购单号、处理时间等字段
 * 调用的函数:
 *   - getFeishuAccessToken() 获取访问令牌
 *   - makeHttpRequest() 发送HTTP请求
 *   - parseFeishuFieldValue() 解析字段值
 * 参数:
 *   baseId (String): 多维表BaseID
 *   tableId (String): 表格ID
 *   recordId (String): 记录ID
 *   statusType (String): 状态类型 (PROCESSING, ORDER_CREATED, COMPLETED, FAILED)
 *   additionalData (Object): 额外数据
 * 返回值: Boolean - 更新是否成功
 * 修改时间: 2025-07-26 15:24
 */
async function updateFeishuRecord(
  baseId,
  tableId,
  recordId,
  statusType,
  additionalData = {}
) {
  try {
    const token = await getFeishuAccessToken();
    if (!token) {
      throw new Error("获取飞书token失败");
    }

    // 获取状态映射（基于实际飞书表格结构）
    const statusMapping = BUSINESS_CONFIG.FEISHU_STATUS_MAPPING[statusType];
    let updateData;

    if (!statusMapping) {
      // 兼容旧的调用方式
      updateData = {
        fields: {
          流程状态: statusType || "",
          采购单: additionalData.purchase_order_id || "",
        },
      };
    } else {
      // 新的状态更新方式（基于实际字段）
      updateData = {
        fields: {
          // 流程状态字段
          流程状态: statusMapping.流程状态,

          // 可选字段
          ...(additionalData.purchase_order_id && {
            采购单: additionalData.purchase_order_id,
          }),
        },
      };
    }

    const url = `${FEISHU_CONFIG.BASE_URL}/bitable/v1/apps/${baseId}/tables/${tableId}/records/${recordId}`;

    const response = await makeHttpRequest(url, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(updateData),
      timeout: FEISHU_CONFIG.API_TIMEOUT,
    });

    if (response && response.code === 0) {
      log("INFO", "飞书记录状态更新成功");
      return true;
    }

    throw new Error(`更新失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `飞书记录更新失败: ${error.message}`);
    return false;
  }
}

/**
 * 通用HTTP请求函数
 *
 * 概述: 发送HTTP请求的通用函数
 * 详细描述: 支持GET、POST、PUT等方法，包含重试机制
 * 调用的函数: 无
 * 参数:
 *   url (String): 请求URL
 *   options (Object): 请求选项
 * 返回值: Object - 响应数据
 * 修改时间: 2025-07-26 14:44
 */
async function makeHttpRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === "https:";
    const httpModule = isHttps ? require("https") : require("http");

    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || "GET",
      headers: options.headers || {},
      timeout: options.timeout || 30000,
    };

    const req = httpModule.request(requestOptions, (res) => {
      let data = "";

      res.on("data", (chunk) => {
        data += chunk;
      });

      res.on("end", () => {
        try {
          const jsonData = JSON.parse(data);
          resolve(jsonData);
        } catch (error) {
          reject(new Error(`JSON解析失败: ${error.message}`));
        }
      });
    });

    req.on("error", (error) => {
      reject(new Error(`请求失败: ${error.message}`));
    });

    req.on("timeout", () => {
      req.destroy();
      reject(new Error("请求超时"));
    });

    if (options.body) {
      req.write(options.body);
    }

    req.end();
  });
}

//===================================================================================
// �🔧 实时分类管理功能
//===================================================================================

/**
 * 实时获取聚水潭最新分类配置
 * @returns {Promise<Object|null>} 最新的分类配置
 */
async function fetchLatestCategories() {
  try {
    log("INFO", "开始获取聚水潭最新分类配置...");

    const allCategories = [];
    let pageIndex = 1;
    const pageSize = 100;
    let hasMore = true;
    let totalCategories = 0;

    while (hasMore) {
      try {
        log("INFO", `正在查询第 ${pageIndex} 页分类数据...`);

        const queryParams = {
          page_index: pageIndex,
          page_size: pageSize,
          c_ids: [],
          parent_c_ids: [],
        };

        const result = await callJushuitanAPI(
          "/open/category/query",
          queryParams,
          `实时分类查询-第${pageIndex}页`
        );

        if (!result) {
          log("WARN", `第 ${pageIndex} 页查询失败，跳过`);
          break;
        }

        const categories = result.datas || [];
        totalCategories += categories.length;

        // 收集分类信息
        categories.forEach((category) => {
          allCategories.push({
            c_id: category.c_id,
            parent_c_id: category.parent_c_id,
            name: category.name,
            modified: category.modified,
          });
        });

        log(
          "INFO",
          `第 ${pageIndex} 页: ${categories.length} 个分类，累计: ${totalCategories} 个`
        );

        // 检查是否还有下一页
        hasMore = result.has_next;
        pageIndex++;

        // 添加延迟避免API频率限制
        if (hasMore) {
          await delay(500);
        }
      } catch (error) {
        log("ERROR", `查询第 ${pageIndex} 页时发生错误: ${error.message}`);
        break;
      }
    }

    if (totalCategories === 0) {
      log("ERROR", "未获取到任何分类数据");
      return null;
    }

    // 构建分类配置
    const categoryNames = [
      ...new Set(
        allCategories.map((cat) => cat.name.trim()).filter((name) => name)
      ),
    ].sort();

    const config = {
      update_time: new Date().toISOString(),
      total_count: totalCategories,
      category_names_count: categoryNames.length,
      full_categories: allCategories,
      category_names: categoryNames,
      source: "real_time_api",
    };

    log(
      "INFO",
      `实时分类获取完成! 共获取 ${totalCategories} 个分类，${categoryNames.length} 个唯一名称`
    );

    // 更新全局缓存
    CATEGORIES_CONFIG = config;

    return config;
  } catch (error) {
    log("ERROR", `实时分类获取失败: ${error.message}`);
    return null;
  }
}

/**
 * 加载分类配置（优先使用实时API，降级到本地文件）
 * @param {boolean} forceRefresh - 是否强制刷新
 * @returns {Promise<Object|null>} 分类配置对象
 */
async function loadCategoriesConfig(forceRefresh = false) {
  // 如果有缓存且不强制刷新，直接返回
  if (CATEGORIES_CONFIG && !forceRefresh) {
    return CATEGORIES_CONFIG;
  }

  try {
    // 1. 优先尝试实时获取最新分类
    log("INFO", "尝试实时获取最新分类配置...");
    const realtimeConfig = await fetchLatestCategories();

    if (realtimeConfig) {
      log(
        "INFO",
        `成功获取实时分类配置: ${realtimeConfig.category_names_count}个可用分类`
      );
      return realtimeConfig;
    }

    // 2. 降级到本地文件
    log("WARN", "实时获取失败，尝试加载本地分类配置...");

    if (typeof require !== "undefined") {
      const fs = require("fs");
      const path = require("path");

      const configPath = path.join(
        __dirname,
        "../配置数据/聚水潭官方分类配置.json"
      );

      if (fs.existsSync(configPath)) {
        const configContent = fs.readFileSync(configPath, "utf8");
        CATEGORIES_CONFIG = JSON.parse(configContent);
        log(
          "INFO",
          `成功加载本地分类配置: ${CATEGORIES_CONFIG.category_names_count}个可用分类 (可能不是最新)`
        );
        return CATEGORIES_CONFIG;
      } else {
        log("WARN", `本地分类配置文件不存在: ${configPath}`);
      }
    }

    log("ERROR", "无法获取分类配置，将跳过分类校验");
    return null;
  } catch (error) {
    log("ERROR", `加载分类配置失败: ${error.message}`);
    return null;
  }
}

/**
 * 检查分类是否为叶子节点
 * @param {string} categoryName - 分类名称
 * @param {Object} config - 分类配置
 * @returns {boolean} 是否为叶子节点
 */
function checkIfLeafNode(categoryName, config) {
  if (!config.category_tree) return true; // 如果没有树结构，默认为叶子节点

  // 递归查找分类节点
  function findNode(nodes, name) {
    for (const node of nodes) {
      if (node.name === name) {
        return node;
      }
      if (node.children && node.children.length > 0) {
        const found = findNode(node.children, name);
        if (found) return found;
      }
    }
    return null;
  }

  const node = findNode(config.category_tree, categoryName);
  return node ? !node.children || node.children.length === 0 : true;
}

/**
 * 获取分类的子分类列表
 * @param {string} categoryName - 分类名称
 * @param {Object} config - 分类配置
 * @returns {Array} 子分类名称列表
 */
function getChildCategories(categoryName, config) {
  if (!config.category_tree) return [];

  // 递归查找分类节点
  function findNode(nodes, name) {
    for (const node of nodes) {
      if (node.name === name) {
        return node;
      }
      if (node.children && node.children.length > 0) {
        const found = findNode(node.children, name);
        if (found) return found;
      }
    }
    return null;
  }

  const node = findNode(config.category_tree, categoryName);
  if (node && node.children) {
    return node.children.map((child) => child.name);
  }
  return [];
}

/**
 * 验证分类名称是否有效（使用最新分类数据）
 * @param {string} categoryName - 分类名称
 * @returns {Promise<Object>} 校验结果
 */
async function validateCategoryName(categoryName) {
  const config = await loadCategoriesConfig();

  if (!config) {
    return {
      valid: false,
      message: "无法加载分类配置，跳过分类校验",
      suggestion: "使用空分类名称",
      categoryToUse: "",
    };
  }

  if (!categoryName || !categoryName.trim()) {
    return {
      valid: true,
      message: "使用空分类名称",
      categoryToUse: "",
    };
  }

  const trimmedName = categoryName.trim();
  const validNames = config.category_names || [];

  if (validNames.includes(trimmedName)) {
    // 检查是否为叶子节点
    const isLeafNode = checkIfLeafNode(trimmedName, config);

    if (isLeafNode) {
      return {
        valid: true,
        message: `分类名称"${trimmedName}"有效且为叶子节点 (来源: ${
          config.source || "local"
        })`,
        categoryToUse: trimmedName,
      };
    } else {
      // 是父节点，需要使用子分类
      const childCategories = getChildCategories(trimmedName, config);
      const recommendedChild =
        childCategories.length > 0 ? childCategories[0] : "T恤";

      return {
        valid: false,
        message: `分类名称"${trimmedName}"是父节点，API要求使用叶子节点`,
        suggestions: childCategories.slice(0, 5), // 最多显示5个建议
        fallback: recommendedChild,
        categoryToUse: recommendedChild,
        nodeType: "parent",
        childrenCount: childCategories.length,
      };
    }
  } else {
    // 尝试智能匹配
    const suggestions = findSimilarCategories(trimmedName, validNames);

    // 智能映射（确保使用叶子节点）
    let fallbackCategory = "";
    const lowerInput = trimmedName.toLowerCase();

    // 定义叶子节点映射规则（使用真正的叶子节点）
    const leafNodeMappings = [
      {
        keywords: ["服装", "衣服", "制衣"],
        candidates: ["基础T恤", "时尚T恤", "衬衫", "短外套"],
      },
      { keywords: ["t恤", "tshirt"], candidates: ["基础T恤", "时尚T恤"] },
      { keywords: ["衬衫", "衬衣"], candidates: ["衬衫"] },
      { keywords: ["外套", "夹克"], candidates: ["短外套", "风衣"] },
      { keywords: ["鞋", "靴"], candidates: ["鞋子"] },
      { keywords: ["裙"], candidates: ["半身裙", "连衣裙"] },
      { keywords: ["裤"], candidates: ["休闲裤", "短裤", "牛仔裤"] },
    ];

    // 查找匹配的映射规则
    for (const mapping of leafNodeMappings) {
      if (mapping.keywords.some((keyword) => lowerInput.includes(keyword))) {
        // 找到第一个存在且为叶子节点的候选分类
        for (const candidate of mapping.candidates) {
          if (
            validNames.includes(candidate) &&
            checkIfLeafNode(candidate, config)
          ) {
            fallbackCategory = candidate;
            break;
          }
        }
        if (fallbackCategory) break;
      }
    }

    // 如果没有找到合适的映射，使用默认的叶子节点
    if (!fallbackCategory) {
      const defaultLeafNodes = ["基础T恤", "时尚T恤", "衬衫", "短外套"];
      for (const defaultNode of defaultLeafNodes) {
        if (
          validNames.includes(defaultNode) &&
          checkIfLeafNode(defaultNode, config)
        ) {
          fallbackCategory = defaultNode;
          break;
        }
      }
    }

    return {
      valid: false,
      message: `分类名称"${trimmedName}"不存在 (已检查最新 ${validNames.length} 个分类)`,
      suggestions: suggestions,
      fallback: fallbackCategory,
      categoryToUse: fallbackCategory || "",
    };
  }
}

/**
 * 查找相似的分类名称
 * @param {string} input - 输入的分类名称
 * @param {Array} validNames - 有效分类名称列表
 * @returns {Array} 相似分类建议
 */
function findSimilarCategories(input, validNames) {
  const suggestions = [];
  const inputLower = input.toLowerCase();

  // 精确匹配
  const exactMatches = validNames.filter(
    (name) => name.toLowerCase() === inputLower
  );

  // 包含匹配
  const containsMatches = validNames.filter(
    (name) =>
      name.toLowerCase().includes(inputLower) ||
      inputLower.includes(name.toLowerCase())
  );

  // 合并并去重
  const allMatches = [...new Set([...exactMatches, ...containsMatches])];

  return allMatches.slice(0, 5);
}

//===================================================================================
// 🏭 供应商编码管理功能
//===================================================================================

/**
 * 生成供应商商品编码
 * @param {string} supplierStyleCode - 供应商款式编码(外采款式编码)
 * @param {string} color - 颜色
 * @param {string} size - 尺码
 * @returns {string} 供应商商品编码
 */
function generateSupplierSkuCode(supplierStyleCode, color, size) {
  // 供应商商品编码 = 供应商款式编码-颜色-尺码
  return `${supplierStyleCode}-${color}-${size}`;
}

/**
 * 生成内部商品编码
 * @param {string} internalStyleCode - 内部款式编码
 * @param {string} color - 颜色
 * @param {string} size - 尺码
 * @returns {string} 内部商品编码
 */
function generateInternalSkuCode(internalStyleCode, color, size) {
  // 内部商品编码 = 内部款式编码-颜色-尺码
  return `${internalStyleCode}-${color}-${size}`;
}

//===================================================================================
// 💼 增强的核心业务逻辑区
//===================================================================================

/**
 * 处理订单数据（增强版：支持供应商编码+实时分类校验）
 * @param {Object} rawData - 原始数据
 * @returns {Promise<Object|null>} 处理后的订单数据
 */
async function processOrderDataEnhanced(rawData) {
  try {
    log("INFO", "开始处理订单数据 (增强版: 供应商编码+实时分类)...");

    const data = typeof rawData === "string" ? JSON.parse(rawData) : rawData;

    // 提取核心字段（使用字段解析函数）
    const internalStyleCode = String(
      parseFeishuFieldValue(data["内部款式编码"]) || ""
    ).trim(); // 内部款式编码
    const supplierStyleCode = String(
      parseFeishuFieldValue(data["外部款式编码"]) || ""
    ).trim(); // 外采款式编码(供应商款式编码)
    const color = String(parseFeishuFieldValue(data["颜色"]) || "").trim();
    const unitPrice = String(
      parseFeishuFieldValue(data["采购单价"]) || "0"
    ).trim();
    const supplier = String(parseFeishuFieldValue(data["档口"]) || "").trim();
    const inputCategory = String(
      parseFeishuFieldValue(data["分类"]) || "基础T恤"
    ).trim(); // 默认使用叶子节点分类

    // 实时分类校验
    log("INFO", `开始实时校验分类: "${inputCategory}"`);
    const categoryValidation = await validateCategoryName(inputCategory);

    if (categoryValidation.valid) {
      log("INFO", `分类校验通过: ${categoryValidation.message}`);
    } else {
      log("WARN", `分类校验失败: ${categoryValidation.message}`);
      if (
        categoryValidation.suggestions &&
        categoryValidation.suggestions.length > 0
      ) {
        log(
          "INFO",
          `建议使用以下分类: ${categoryValidation.suggestions.join(", ")}`
        );
      }
      log("INFO", `将使用智能映射分类: "${categoryValidation.categoryToUse}"`);
    }

    const validatedCategory = categoryValidation.categoryToUse || "";

    // 提取图片URL
    let imageUrl = "";
    const images = data["图片"];
    if (Array.isArray(images) && images.length > 0) {
      const imageObject = images[0];
      if (imageObject && typeof imageObject === "object" && imageObject.url) {
        imageUrl = imageObject.url;
        log("INFO", `成功提取图片URL: ${imageUrl}`);
      } else {
        log("WARN", "图片字段格式不符合预期，未找到URL。");
      }
    }

    // 提取尺码数量
    const sizes = {};
    const sizeFields = [
      { field: "S", code: "S" },
      { field: "M", code: "M" },
      { field: "L", code: "L" },
      { field: "均码（F）", code: "F" },
    ];

    sizeFields.forEach((sizeField) => {
      const qty = data[sizeField.field];
      if (qty && String(qty).trim() && String(qty).trim() !== "0") {
        try {
          const quantity = parseInt(String(qty).trim());
          if (quantity > 0) {
            sizes[sizeField.code] = quantity;
          }
        } catch (e) {
          log("WARN", `尺码${sizeField.field}数量解析失败: ${qty}`);
        }
      }
    });

    // 数据验证
    if (!internalStyleCode) {
      throw new Error("缺少内部款式编码");
    }
    if (!supplierStyleCode) {
      log("WARN", "缺少外部款式编码(供应商款式编码)，将使用内部款式编码");
    }
    if (!color) {
      throw new Error("缺少颜色信息");
    }
    if (Object.keys(sizes).length === 0) {
      throw new Error("缺少有效的尺码数量");
    }

    const processedData = {
      internalStyleCode: internalStyleCode, // 内部款式编码
      supplierStyleCode: supplierStyleCode || internalStyleCode, // 供应商款式编码(外采款式编码)
      color: color,
      unitPrice: parseFloat(unitPrice) || 0,
      supplier: supplier,
      category: validatedCategory, // 校验后的分类
      originalCategory: inputCategory, // 原始分类
      categoryValidation: categoryValidation, // 完整校验信息
      sizes: sizes,
      imageUrl: imageUrl,
    };

    log("INFO", `订单数据处理成功:`);
    log("INFO", `  内部款式: ${internalStyleCode}`);
    log("INFO", `  供应商款式: ${supplierStyleCode || internalStyleCode}`);
    log("INFO", `  颜色: ${color}`);
    log("INFO", `  分类: "${validatedCategory}" (原始: "${inputCategory}")`);
    log("INFO", `  尺码数量: ${Object.keys(sizes).length}个`);

    return processedData;
  } catch (error) {
    log("ERROR", `订单数据处理失败: ${error.message}`);
    return null;
  }
}

/**
 * 生成增强的SKU列表（包含供应商编码）
 * @param {Object} orderData - 处理后的订单数据
 * @returns {Array|null} 增强的SKU列表
 */
function generateEnhancedSKUList(orderData) {
  try {
    log("INFO", "开始生成增强SKU列表 (包含供应商编码)...");

    const skuList = [];
    const {
      internalStyleCode,
      supplierStyleCode,
      color,
      unitPrice,
      sizes,
      category,
    } = orderData;

    Object.keys(sizes).forEach((size) => {
      const quantity = sizes[size];

      // 生成内部商品编码
      const internalSku = generateInternalSkuCode(
        internalStyleCode,
        color,
        size
      );

      // 生成供应商商品编码
      const supplierSku = generateSupplierSkuCode(
        supplierStyleCode,
        color,
        size
      );

      skuList.push({
        sku: internalSku, // 内部商品编码 (作为主SKU)
        internalStyleCode: internalStyleCode, // 内部款式编码
        supplierStyleCode: supplierStyleCode, // 供应商款式编码 (外采款式编码)
        supplierSku: supplierSku, // 供应商商品编码
        color: color,
        size: size,
        quantity: quantity,
        unitPrice: unitPrice,
        category: category,
      });
    });

    log("INFO", `增强SKU列表生成成功: ${skuList.length}个SKU`);
    skuList.forEach((item) => {
      log("DEBUG", `  内部SKU: ${item.sku}`);
      log("DEBUG", `  供应商SKU: ${item.supplierSku}`);
      log(
        "DEBUG",
        `  数量: ${item.quantity}, 单价: ${item.unitPrice}, 分类: "${item.category}"`
      );
    });

    return skuList;
  } catch (error) {
    log("ERROR", `SKU生成失败: ${error.message}`);
    return null;
  }
}

//===================================================================================
// 🏭 增强的聚水潭业务功能
//===================================================================================

/**
 * 验证和创建供应商
 * @param {string} supplierName - 供应商名称
 * @returns {Promise<number|null>} 供应商ID
 */
async function verifyAndCreateSupplier(supplierName) {
  try {
    log("INFO", `开始验证和创建供应商: ${supplierName}`);

    // 1. 查询供应商是否存在
    const queryParams = {
      names: [supplierName],
      page_index: 1,
      page_size: 1,
    };

    const queryResult = await callJushuitanAPI(
      "/open/supplier/query",
      queryParams,
      `供应商查询-${supplierName}`
    );

    if (queryResult && queryResult.datas && queryResult.datas.length > 0) {
      const supplierId = queryResult.datas[0].supplier_id;
      log("INFO", `供应商已存在: ${supplierName}, ID: ${supplierId}`);
      return supplierId;
    }

    // 2. 创建供应商
    log("INFO", `供应商不存在，开始创建: ${supplierName}`);
    const createParams = [
      {
        name: supplierName,
        supplier_code: `SUP_${Date.now().toString().slice(-10)}`,
        enabled: true,
      },
    ];

    const createResult = await callJushuitanAPI(
      "/open/supplier/upload",
      createParams,
      `供应商创建-${supplierName}`
    );

    if (
      createResult &&
      createResult.datas &&
      createResult.datas.length > 0 &&
      createResult.datas[0].issuccess
    ) {
      const newSupplierId = createResult.datas[0].id;
      log("INFO", `供应商创建成功: ${supplierName}, ID: ${newSupplierId}`);
      return newSupplierId;
    } else {
      throw new Error(`供应商创建失败: ${JSON.stringify(createResult)}`);
    }
  } catch (error) {
    log("ERROR", `供应商验证或创建失败: ${error.message}`);
    throw error;
  }
}

/**
 * 验证和创建商品（增强版：包含供应商编码信息）
 * @param {Array} skuList - 增强的SKU列表
 * @param {string} imageUrl - 商品图片URL
 * @returns {Promise<Array|null>} 验证通过的SKU列表
 */
async function verifyAndCreateProductsEnhanced(skuList, imageUrl) {
  try {
    log("INFO", "开始验证和创建商品 (增强版: 包含供应商编码)...");

    const verifiedSkus = [];

    for (let i = 0; i < skuList.length; i++) {
      const skuInfo = skuList[i];
      const sku = skuInfo.sku;

      log("INFO", `验证商品 ${i + 1}/${skuList.length}: ${sku}`);
      log("DEBUG", `  供应商SKU: ${skuInfo.supplierSku}`);
      log("DEBUG", `  分类: "${skuInfo.category}"`);

      // 查询商品是否存在
      const queryParams = {
        sku_ids: sku,
        page_index: 1,
        page_size: 1,
      };

      const queryResult = await callJushuitanAPI(
        "/open/sku/query",
        queryParams,
        `商品查询-${sku}`
      );

      let exists = false;
      if (queryResult && queryResult.datas) {
        const goods = queryResult.datas || [];
        exists = goods.length > 0;
      }

      if (!exists) {
        log("INFO", `商品不存在，开始创建: ${sku}`);

        // 增强的商品创建参数
        const createParams = {
          items: [
            {
              sku_id: sku, // 内部商品编码
              i_id: skuInfo.internalStyleCode, // 内部款式编码
              name: `${skuInfo.internalStyleCode} ${skuInfo.color} ${skuInfo.size}码`,
              brand: "AHMI",
              c_name: skuInfo.category, // 校验后的分类
              pic: imageUrl || "",
              enabled: 1,
              batch_enabled: false,
              is_series_number: false,
              properties_value: `${skuInfo.color};${skuInfo.size}`,
              s_price: skuInfo.unitPrice || 0,
              supplier_i_id: skuInfo.supplierStyleCode, // 供应商款式编码 (外采款式编码)
              supplier_sku_id: skuInfo.supplierSku, // 供应商商品编码
            },
          ],
        };

        const createResult = await callJushuitanAPI(
          "/open/jushuitan/itemsku/upload",
          createParams,
          `商品创建-${sku}`
        );

        if (!createResult || !createResult.datas) {
          log("ERROR", `商品创建失败: ${sku}`);
          continue;
        }

        const createData = createResult.datas[0];
        if (createData && createData.is_success) {
          log("INFO", `商品创建成功: ${sku}`);
          log(
            "DEBUG",
            `  供应商编码信息已设置: ${skuInfo.supplierStyleCode} / ${skuInfo.supplierSku}`
          );
        } else {
          log(
            "ERROR",
            `商品创建失败: ${sku}, 消息: ${
              createData && createData.msg ? createData.msg : "未知错误"
            }`
          );
          continue;
        }
      } else {
        log("INFO", `商品已存在: ${sku}`);
      }

      verifiedSkus.push(skuInfo);

      // 添加延迟，避免API频率限制
      await delay(500);
    }

    log(
      "INFO",
      `商品验证完成: ${verifiedSkus.length}/${skuList.length}个商品可用`
    );
    return verifiedSkus;
  } catch (error) {
    log("ERROR", `商品验证失败: ${error.message}`);
    return null;
  }
}

/**
 * 创建采购单（增强版）
 * @param {Array} skuList - 验证通过的SKU列表
 * @param {Object} orderData - 订单数据
 * @param {number} supplierId - 供应商ID
 * @returns {Promise<string|null>} 采购单ID
 */
async function createPurchaseOrderEnhanced(skuList, orderData, supplierId) {
  try {
    log("INFO", "开始创建采购单 (增强版)...");

    // 构建采购单商品列表
    const items = skuList.map((skuInfo) => ({
      sku_id: skuInfo.sku,
      qty: skuInfo.quantity,
      price: skuInfo.unitPrice,
    }));

    // 生成外部单号
    const externalId = `ENH_${Date.now()}_${Math.floor(Math.random() * 1000)
      .toString()
      .padStart(3, "0")}`;

    const createParams = {
      supplier_id: supplierId,
      external_id: externalId,
      wms_co_id: JUSHUITAN_CONFIG.DEFAULT_WMS_CO_ID,
      items: items,
      remark: `增强版API测试 - ${orderData.internalStyleCode}(${orderData.supplierStyleCode}) ${orderData.color} (分类: ${orderData.category}) [实时分类更新+供应商编码]`,
      is_confirm: true,
    };

    log("INFO", `采购单参数: ${JSON.stringify(createParams)}`);

    const result = await callJushuitanAPI(
      "/open/jushuitan/purchase/upload",
      createParams,
      "增强版采购单创建"
    );

    if (result && result.data && result.data.po_id) {
      const purchaseOrderId = result.data.po_id;
      log("INFO", `采购单创建成功: ${purchaseOrderId}`);
      return purchaseOrderId;
    } else {
      throw new Error(`采购单创建失败: ${JSON.stringify(result)}`);
    }
  } catch (error) {
    log("ERROR", `采购单创建失败: ${error.message}`);
    return null;
  }
}

//===================================================================================
// 🚀 增强版主测试流程
//===================================================================================

/**
 * 完整的增强业务流程测试
 * @returns {Promise<Object>} 测试结果
 */
async function testEnhancedCompleteFlow() {
  const startTime = Date.now();
  log("INFO", "=== 开始增强版完整业务流程测试 ===");
  log("INFO", "新功能: 实时分类更新 + 供应商编码管理");

  try {
    // 第0步: 实时获取最新分类配置
    log("INFO", "第0步: 实时获取最新分类配置");
    await loadCategoriesConfig(true); // 强制刷新

    // 第1步: 处理订单数据（增强版）
    log("INFO", "第1步: 处理订单数据（增强版: 供应商编码+实时分类）");
    const orderData = await processOrderDataEnhanced(TEST_DATA.feishuData);
    if (!orderData) {
      throw new Error("订单数据处理失败");
    }

    // 第2步: 验证和创建供应商
    log("INFO", "第2步: 验证和创建供应商");
    const supplierId = await verifyAndCreateSupplier(orderData.supplier);
    if (!supplierId) {
      throw new Error("获取供应商ID失败");
    }

    // 第3步: 生成增强SKU列表
    log("INFO", "第3步: 生成增强SKU列表（包含供应商编码）");
    const skuList = generateEnhancedSKUList(orderData);
    if (!skuList || skuList.length === 0) {
      throw new Error("SKU生成失败");
    }

    // 第4步: 验证和创建商品（增强版）
    log("INFO", "第4步: 验证和创建商品（增强版: 包含供应商编码）");
    const verifiedSkus = await verifyAndCreateProductsEnhanced(
      skuList,
      orderData.imageUrl
    );
    if (!verifiedSkus || verifiedSkus.length === 0) {
      throw new Error("商品验证失败");
    }

    // 第5步: 创建采购单（增强版）
    log("INFO", "第5步: 创建采购单（增强版）");
    const purchaseOrderId = await createPurchaseOrderEnhanced(
      verifiedSkus,
      orderData,
      supplierId
    );
    if (!purchaseOrderId) {
      throw new Error("采购单创建失败");
    }

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    log("INFO", "=== 增强版测试完成 ===");
    log("INFO", `处理时间: ${duration}秒`);
    log("INFO", `采购单号: ${purchaseOrderId}`);
    log("INFO", `处理SKU: ${verifiedSkus.length}个`);
    log("INFO", `供应商ID: ${supplierId}`);
    log("INFO", `内部款式: ${orderData.internalStyleCode}`);
    log("INFO", `供应商款式: ${orderData.supplierStyleCode}`);
    log("INFO", `使用分类: "${orderData.category}" (实时校验)`);
    log("INFO", `分类来源: ${orderData.categoryValidation.message}`);

    return {
      status: "success",
      data: {
        purchase_order_id: purchaseOrderId,
        supplier_id: supplierId,
        processed_skus: verifiedSkus.length,
        processing_time: duration,
        internal_style_code: orderData.internalStyleCode,
        supplier_style_code: orderData.supplierStyleCode,
        category_used: orderData.category,
        original_category: orderData.originalCategory,
        category_validation: orderData.categoryValidation,
        enhanced_features: [
          "实时分类更新",
          "供应商编码管理",
          "智能分类映射",
          "增强商品参数",
        ],
      },
    };
  } catch (error) {
    log("ERROR", "=== 增强版测试失败 ===");
    log("ERROR", `错误信息: ${error.message}`);

    return {
      status: "error",
      message: error.message,
    };
  }
}

//===================================================================================
// 🔍 数据校验模块
//===================================================================================

/**
 * 执行数据完整性校验
 *
 * 概述: 校验飞书记录和聚水潭数据是否成功创建
 * 详细描述: 验证飞书记录状态、聚水潭商品、采购单、供应商是否真实存在
 * 调用的函数:
 *   - getFeishuRecord() 获取飞书记录
 *   - callJushuitanAPI() 调用聚水潭API
 * 参数:
 *   verificationData (Object): 校验数据
 * 返回值: Object - 校验结果
 * 修改时间: 2025-07-26 15:38
 */
async function performDataVerification(verificationData) {
  try {
    log("INFO", "开始执行数据完整性校验...");

    const results = {
      feishu_verification: null,
      products_verification: [],
      purchase_order_verification: null,
      supplier_verification: null,
      overall_status: "success",
      verification_summary: {},
    };

    // 1. 校验飞书记录
    if (verificationData.feishu) {
      log("INFO", "校验飞书记录状态...");
      const { baseId, tableId, recordId } = verificationData.feishu;

      try {
        const feishuRecord = await getFeishuRecord(baseId, tableId, recordId);
        if (feishuRecord) {
          results.feishu_verification = {
            status: "success",
            record_exists: true,
            fields_count: Object.keys(feishuRecord.fields).length,
            flow_status: feishuRecord.fields["流程状态"] || null,
            purchase_order: feishuRecord.fields["采购单"] || null,
          };
          log(
            "INFO",
            `飞书记录校验成功: 字段数=${results.feishu_verification.fields_count}`
          );
        }
      } catch (error) {
        results.feishu_verification = {
          status: "error",
          record_exists: false,
          message: error.message,
        };
        log("ERROR", `飞书记录校验失败: ${error.message}`);
      }
    }

    // 2. 校验聚水潭商品（使用sku_ids参数）
    if (verificationData.jushuitan?.products) {
      log("INFO", "校验聚水潭商品...");
      for (const productSku of verificationData.jushuitan.products) {
        try {
          const queryResult = await callJushuitanAPI(
            "/open/sku/query",
            { sku_ids: productSku, page_index: 1, page_size: 1 },
            `商品校验-${productSku}`
          );

          if (
            queryResult &&
            queryResult.datas &&
            queryResult.datas.length > 0
          ) {
            const product = queryResult.datas[0];
            results.products_verification.push({
              sku: productSku,
              status: "success",
              exists: true,
              product_info: {
                name: product.name,
                category: product.category,
                created: product.created,
              },
            });
            log("INFO", `商品校验成功: ${productSku}`);
          } else {
            results.products_verification.push({
              sku: productSku,
              status: "not_found",
              exists: false,
            });
            log("WARN", `商品不存在: ${productSku}`);
          }
        } catch (error) {
          results.products_verification.push({
            sku: productSku,
            status: "error",
            exists: false,
            message: error.message,
          });
          log("ERROR", `商品校验失败: ${productSku} - ${error.message}`);
        }
      }
    }

    // 3. 校验采购单（使用po_ids参数）
    if (verificationData.jushuitan?.purchase_order_id) {
      log("INFO", "校验聚水潭采购单...");
      const purchaseOrderId = verificationData.jushuitan.purchase_order_id;

      // 尝试多种查询格式，包含延迟重试
      const queryVariations = [
        { po_ids: [String(purchaseOrderId)] }, // 字符串数组
        { po_ids: String(purchaseOrderId) }, // 字符串
        { po_id: purchaseOrderId }, // 单个ID (数字)
        { po_id: String(purchaseOrderId) }, // 单个ID (字符串)
      ];

      let success = false;
      for (let attempt = 0; attempt < 3 && !success; attempt++) {
        // 第一次立即查询，后续延迟查询
        if (attempt > 0) {
          const delay = 2000 * attempt; // 2秒、4秒延迟
          log(
            "INFO",
            `采购单校验延迟重试 ${attempt + 1}/3，等待 ${delay}ms...`
          );
          await new Promise((resolve) => setTimeout(resolve, delay));
        }

        for (const params of queryVariations) {
          try {
            const queryResult = await callJushuitanAPI(
              "/open/purchase/query",
              { ...params, page_index: 1, page_size: 1 },
              `采购单校验-${purchaseOrderId}-格式${
                queryVariations.indexOf(params) + 1
              }-重试${attempt + 1}`
            );

            if (
              queryResult &&
              queryResult.datas &&
              queryResult.datas.length > 0
            ) {
              const purchaseOrder = queryResult.datas[0];
              results.purchase_order_verification = {
                status: "success",
                exists: true,
                po_id: purchaseOrderId,
                retry_attempt: attempt + 1,
                successful_format: queryVariations.indexOf(params) + 1,
                order_info: {
                  supplier_id: purchaseOrder.supplier_id,
                  status: purchaseOrder.status,
                  po_date: purchaseOrder.po_date,
                  seller: purchaseOrder.seller,
                },
              };
              log(
                "INFO",
                `采购单校验成功: ${purchaseOrderId} (重试${
                  attempt + 1
                }次，格式${queryVariations.indexOf(params) + 1})`
              );
              success = true;
              break;
            }
          } catch (error) {
            log(
              "WARN",
              `采购单校验格式${queryVariations.indexOf(params) + 1}重试${
                attempt + 1
              }失败: ${error.message}`
            );
          }
        }

        // 如果已经成功，跳出外层循环
        if (success) {
          break;
        }
      }

      if (!success) {
        results.purchase_order_verification = {
          status: "not_found",
          exists: false,
          po_id: purchaseOrderId,
          total_attempts: 3,
          formats_tried: queryVariations.length,
        };
        log(
          "WARN",
          `采购单校验最终失败: ${purchaseOrderId} (尝试3次重试，${queryVariations.length}种格式)`
        );
      }
    }

    // 4. 校验供应商（使用supplier_ids参数）
    if (verificationData.jushuitan?.supplier_id) {
      log("INFO", "校验聚水潭供应商...");
      const supplierId = verificationData.jushuitan.supplier_id;

      // 尝试多种查询格式
      const queryVariations = [
        { supplier_ids: [String(supplierId)] }, // 字符串数组
        { supplier_ids: String(supplierId) }, // 字符串
        { supplier_id: supplierId }, // 单个ID (数字)
        { supplier_id: String(supplierId) }, // 单个ID (字符串)
        {
          supplier_ids: [String(supplierId)],
          enabled: 1, // 只查询启用的供应商
        },
        {
          supplier_ids: [String(supplierId)],
          enabled: 0, // 查询禁用的供应商
        },
      ];

      let success = false;
      for (const params of queryVariations) {
        try {
          const queryResult = await callJushuitanAPI(
            "/open/supplier/query",
            { ...params, page_index: 1, page_size: 1 },
            `供应商校验-${supplierId}-格式${
              queryVariations.indexOf(params) + 1
            }`
          );

          if (
            queryResult &&
            queryResult.datas &&
            queryResult.datas.length > 0
          ) {
            const supplier = queryResult.datas[0];
            results.supplier_verification = {
              status: "success",
              exists: true,
              supplier_id: supplierId,
              successful_format: queryVariations.indexOf(params) + 1,
              supplier_info: {
                name: supplier.name,
                supplier_code: supplier.supplier_code,
                enabled: supplier.enabled,
                modified: supplier.modified,
              },
            };
            log(
              "INFO",
              `供应商校验成功: ${supplierId} (格式${
                queryVariations.indexOf(params) + 1
              })`
            );
            success = true;
            break;
          }
        } catch (error) {
          log(
            "WARN",
            `供应商校验格式${queryVariations.indexOf(params) + 1}失败: ${
              error.message
            }`
          );
        }
      }

      if (!success) {
        results.supplier_verification = {
          status: "not_found",
          exists: false,
          supplier_id: supplierId,
          formats_tried: queryVariations.length,
        };
        log(
          "WARN",
          `供应商校验最终失败: ${supplierId} (尝试${queryVariations.length}种格式)`
        );
      }
    }

    // 5. 生成校验摘要
    const successCount = [
      results.feishu_verification?.status === "success" ? 1 : 0,
      results.products_verification.filter((p) => p.status === "success")
        .length,
      results.purchase_order_verification?.status === "success" ? 1 : 0,
      results.supplier_verification?.status === "success" ? 1 : 0,
    ].reduce((a, b) => a + b, 0);

    const totalCount = [
      results.feishu_verification ? 1 : 0,
      results.products_verification.length,
      results.purchase_order_verification ? 1 : 0,
      results.supplier_verification ? 1 : 0,
    ].reduce((a, b) => a + b, 0);

    results.verification_summary = {
      total_items: totalCount,
      success_items: successCount,
      success_rate:
        totalCount > 0
          ? ((successCount / totalCount) * 100).toFixed(2) + "%"
          : "0%",
      overall_status:
        successCount === totalCount ? "success" : "partial_success",
    };

    log(
      "INFO",
      `数据校验完成: ${successCount}/${totalCount} 项成功 (${results.verification_summary.success_rate})`
    );

    return results;
  } catch (error) {
    log("ERROR", `数据校验失败: ${error.message}`);
    return {
      overall_status: "error",
      message: error.message,
    };
  }
}

//===================================================================================
// 🔗 飞书数据处理流程
//===================================================================================

/**
 * 处理飞书数据的完整流程
 *
 * 概述: 从飞书获取数据并创建采购单的完整流程
 * 详细描述: 获取飞书记录数据，处理订单，创建采购单，更新状态
 * 调用的函数:
 *   - getFeishuRecord() 获取飞书记录
 *   - processOrderDataEnhanced() 处理订单数据
 *   - generateEnhancedSKUList() 生成SKU列表
 *   - verifyAndCreateSupplier() 验证创建供应商
 *   - verifyAndCreateProductsEnhanced() 验证创建商品
 *   - createPurchaseOrderEnhanced() 创建采购单
 *   - updateFeishuRecord() 更新飞书状态
 * 参数:
 *   baseId (String): 飞书多维表BaseID
 *   tableId (String): 飞书表格ID
 *   recordId (String): 飞书记录ID
 * 返回值: Object - 处理结果
 * 修改时间: 2025-07-26 14:44
 */
async function processFeishuDataFlow(baseId, tableId, recordId) {
  const startTime = Date.now();
  log("INFO", "=== 开始飞书数据处理流程 ===");
  log("INFO", `记录ID: ${recordId}`);

  try {
    // 1. 更新状态为处理中
    await updateFeishuRecord(baseId, tableId, recordId, "PROCESSING");

    // 2. 获取飞书记录数据
    log("INFO", "第1步: 获取飞书记录数据");
    const feishuRecord = await getFeishuRecord(baseId, tableId, recordId);
    if (!feishuRecord) {
      throw new Error("获取飞书记录失败");
    }

    log(
      "INFO",
      `飞书记录获取成功，字段数: ${Object.keys(feishuRecord.fields).length}`
    );

    // 3. 实时获取最新分类配置
    log("INFO", "第2步: 实时获取最新分类配置");
    await loadCategoriesConfig(true); // 强制刷新

    // 4. 处理订单数据（增强版）
    log("INFO", "第3步: 处理订单数据（增强版: 供应商编码+实时分类）");
    const orderData = await processOrderDataEnhanced(feishuRecord.fields);
    if (!orderData) {
      throw new Error("订单数据处理失败");
    }

    // 5. 生成增强SKU列表
    log("INFO", "第4步: 生成增强SKU列表（内部+供应商编码）");
    const skuList = generateEnhancedSKUList(orderData);
    if (!skuList || skuList.length === 0) {
      throw new Error("SKU列表生成失败");
    }

    // 6. 验证/创建供应商
    log("INFO", "第5步: 验证/创建供应商");
    const supplierId = await verifyAndCreateSupplier(orderData.supplier);
    if (!supplierId) {
      throw new Error("供应商验证/创建失败");
    }

    // 7. 验证/创建商品（增强版）
    log("INFO", "第6步: 验证/创建商品（增强版: 供应商编码+分类校验）");
    const verifiedSkus = await verifyAndCreateProductsEnhanced(
      skuList,
      orderData.imageUrl
    );
    if (!verifiedSkus || verifiedSkus.length === 0) {
      throw new Error("商品验证/创建失败");
    }

    // 8. 创建采购单（增强版）
    log("INFO", "第7步: 创建采购单（增强版: 供应商编码+实时分类）");
    const purchaseOrderId = await createPurchaseOrderEnhanced(
      verifiedSkus,
      orderData,
      supplierId
    );
    if (!purchaseOrderId) {
      throw new Error("采购单创建失败");
    }

    // 9. 更新成功状态（采购单创建成功）
    await updateFeishuRecord(baseId, tableId, recordId, "ORDER_CREATED", {
      purchase_order_id: purchaseOrderId,
    });

    // 10. 数据校验（验证创建的数据是否真实存在）
    log("INFO", "第8步: 数据完整性校验");
    const verificationResults = await performDataVerification({
      feishu: { baseId, tableId, recordId },
      jushuitan: {
        products: verifiedSkus.map((sku) => sku.sku),
        purchase_order_id: purchaseOrderId,
        supplier_id: supplierId,
      },
    });

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    log("INFO", "=== 飞书数据处理流程完成 ===");
    log("INFO", `处理耗时: ${duration}秒`);
    log("INFO", `采购单号: ${purchaseOrderId}`);
    log("INFO", `处理SKU: ${verifiedSkus.length}个`);
    log("INFO", `供应商ID: ${supplierId}`);
    log("INFO", `内部款式: ${orderData.internalStyleCode}`);
    log("INFO", `供应商款式: ${orderData.supplierStyleCode}`);
    log("INFO", `使用分类: "${orderData.category}" (实时校验)`);
    log("INFO", `分类来源: ${orderData.categoryValidation.message}`);

    return {
      status: "success",
      data: {
        purchase_order_id: purchaseOrderId,
        supplier_id: supplierId,
        processed_skus: verifiedSkus.length,
        processing_time: duration,
        internal_style_code: orderData.internalStyleCode,
        supplier_style_code: orderData.supplierStyleCode,
        category_used: orderData.category,
        original_category: orderData.originalCategory,
        category_validation: orderData.categoryValidation,
        feishu_record_id: recordId,
        verification_results: verificationResults,
        enhanced_features: [
          "飞书数据获取",
          "实时分类更新",
          "供应商编码管理",
          "智能分类映射",
          "增强商品参数",
          "状态自动回写",
          "数据完整性校验",
        ],
      },
    };
  } catch (error) {
    log("ERROR", `=== 飞书数据处理失败 ===`);
    log("ERROR", `错误信息: ${error.message}`);

    // 更新失败状态
    await updateFeishuRecord(baseId, tableId, recordId, "FAILED", {
      error: error.message,
    });

    return {
      status: "error",
      message: error.message,
      feishu_record_id: recordId,
    };
  }
}

//===================================================================================
// 🎯 主入口函数
//===================================================================================

/**
 * 主函数入口
 * @param {string} mode - 运行模式: "test"(测试模式) 或 "feishu"(飞书模式)
 * @param {Object} options - 选项参数
 * @returns {Promise<Object>} 执行结果
 */
async function main(mode = "test", options = {}) {
  try {
    log("INFO", "开始执行聚水潭API增强版 (实时分类+供应商编码+飞书集成)...");
    log("INFO", `运行模式: ${mode}`);

    let result;

    switch (mode) {
      case "feishu":
        // 飞书数据处理模式
        const { baseId, tableId, recordId } = options;
        if (!baseId || !tableId || !recordId) {
          throw new Error("飞书模式需要提供 baseId, tableId, recordId 参数");
        }
        log(
          "INFO",
          `飞书参数: baseId=${baseId}, tableId=${tableId}, recordId=${recordId}`
        );
        result = await processFeishuDataFlow(baseId, tableId, recordId);
        break;

      case "test":
      case "enhanced":
      case "complete":
      default:
        // 测试模式（使用模拟数据）
        result = await testEnhancedCompleteFlow();
        break;
    }

    log("INFO", "增强版API执行完毕。");
    return result;
  } catch (error) {
    log("ERROR", `执行发生致命错误: ${error.message}`);
    return {
      status: "error",
      message: error.message,
    };
  }
}

//===================================================================================
// 🔧 模块导出和直接执行支持
//===================================================================================

// 如果在Node.js环境中直接执行此文件
if (typeof require !== "undefined" && require.main === module) {
  const mode = process.argv[2] || "test";

  // 解析命令行参数
  let options = {};
  if (mode === "feishu") {
    // 飞书模式需要额外参数
    // 支持两种用法：
    // 1. node script.js feishu <recordId>
    // 2. node script.js feishu <baseId> <tableId> <recordId>
    if (process.argv.length === 4) {
      // 简化用法：只提供recordId
      options = {
        baseId: FEISHU_CONFIG.BASE_ID,
        tableId: FEISHU_CONFIG.TABLE_ID,
        recordId: process.argv[3],
      };
    } else {
      // 完整用法：提供所有参数
      options = {
        baseId: process.argv[3] || FEISHU_CONFIG.BASE_ID,
        tableId: process.argv[4] || FEISHU_CONFIG.TABLE_ID,
        recordId: process.argv[5],
      };
    }

    if (!options.recordId) {
      console.error("飞书模式需要提供recordId参数");
      console.error(
        "用法: node 聚水潭ERP集成脚本.js feishu [baseId] [tableId] <recordId>"
      );
      process.exit(1);
    }
  }

  main(mode, options)
    .then((result) => {
      console.log("=== 增强版最终结果 ===");
      console.log(JSON.stringify(result, null, 2));
      process.exit(result && result.status === "success" ? 0 : 1);
    })
    .catch((error) => {
      console.error("执行错误:", error);
      process.exit(1);
    });
}

// 模块导出
if (typeof module !== "undefined" && module.exports) {
  module.exports = {
    // 主入口函数
    main,

    // 飞书数据处理
    processFeishuDataFlow,
    getFeishuAccessToken,
    getFeishuRecord,
    updateFeishuRecord,
    makeHttpRequest,

    // 测试和业务流程
    testEnhancedCompleteFlow,
    processOrderDataEnhanced,
    generateEnhancedSKUList,

    // 分类管理
    fetchLatestCategories,
    loadCategoriesConfig,
    validateCategoryName,
    checkIfLeafNode,
    getChildCategories,

    // SKU和编码生成
    generateSupplierSkuCode,
    generateInternalSkuCode,

    // ERP业务操作
    verifyAndCreateSupplier,
    verifyAndCreateProductsEnhanced,
    createPurchaseOrderEnhanced,
    callJushuitanAPI,

    // 配置对象
    JUSHUITAN_CONFIG,
    FEISHU_CONFIG,
    BUSINESS_CONFIG,
    TEST_DATA,
  };
}

// 浏览器环境全局导出
if (typeof window !== "undefined") {
  window.JushuitanAPIEnhanced = {
    main,
    testEnhancedCompleteFlow,
    processOrderDataEnhanced,
    generateEnhancedSKUList,
    fetchLatestCategories,
    loadCategoriesConfig,
    validateCategoryName,
    generateSupplierSkuCode,
    generateInternalSkuCode,
    callJushuitanAPI,
    JUSHUITAN_CONFIG,
    TEST_DATA,
  };
}
