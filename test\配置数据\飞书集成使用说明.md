# 🔗 飞书集成使用说明

## 📋 功能概述

聚水潭ERP集成脚本现已支持飞书多维表数据获取，实现从飞书外采下单表到聚水潭ERP的完整自动化流程。

## 🎯 新增功能

### 1. 飞书数据获取
- ✅ 自动获取飞书多维表记录数据
- ✅ 支持飞书API认证和访问令牌管理
- ✅ 完整的错误处理和重试机制

### 2. 状态自动回写
- ✅ 处理状态实时更新（待处理→处理中→已完成/失败）
- ✅ 采购单号自动回写到飞书表格
- ✅ 错误信息详细记录

### 3. 智能分类处理
- ✅ 叶子节点分类验证（修复：使用"基础T恤"等真正的叶子节点）
- ✅ 实时分类配置更新
- ✅ 智能分类映射和建议

## 🔧 使用方法

### 测试模式（使用模拟数据）
```bash
# 基本测试
node "test\核心脚本\聚水潭ERP集成脚本.js" test

# 或者使用默认参数
node "test\核心脚本\聚水潭ERP集成脚本.js"
```

### 飞书模式（处理真实飞书数据）
```bash
# 使用默认配置
node "test\核心脚本\聚水潭ERP集成脚本.js" feishu <recordId>

# 指定完整参数
node "test\核心脚本\聚水潭ERP集成脚本.js" feishu <baseId> <tableId> <recordId>
```

### 参数说明
- `baseId`: 飞书多维表BaseID（默认：E1Q3bDq3harjR1s8qr3cyKz6n1b）
- `tableId`: 飞书表格ID（默认：tblwJFuLDpV62Z9p）
- `recordId`: 飞书记录ID（必须提供）

## 📊 飞书字段映射

### 输入字段（从飞书获取）
```
内部款式编码 → style_code
外部款式编码 → external_style_code  
颜色 → color
S → size_s_qty
M → size_m_qty
L → size_l_qty
均码（F） → size_f_qty
采购单价 → unit_price
采购日期 → purchase_date
采购人员 → purchaser
档口 → supplier_stall
图片 → product_images
备注 → remark
成分 → material
```

### 输出字段（回写到飞书）
```
处理状态 → status（待处理/处理中/已完成/失败）
采购单号 → purchase_order_id
处理时间 → process_time
错误信息 → error_message
```

## 🔑 配置信息

### 飞书API配置
```javascript
const FEISHU_CONFIG = {
  APP_ID: "cli_a73da8fdc6fe900d",
  APP_SECRET: "CM0Vl3kDoKUHRim3JuJznXT1rqTBbrQa",
  BASE_URL: "https://open.feishu.cn/open-apis",
  
  // 多维表信息
  BASE_ID: "E1Q3bDq3harjR1s8qr3cyKz6n1b",
  TABLE_ID: "tblwJFuLDpV62Z9p",
  VIEW_ID: "vewoihqbAb",
};
```

### 业务配置
```javascript
const BUSINESS_CONFIG = {
  // 状态枚举
  STATUS_ENUM: {
    PENDING: "待处理",
    PROCESSING: "处理中", 
    COMPLETED: "已完成",
    FAILED: "失败",
  },
  
  // 尺码映射
  SIZE_MAPPING: {
    S: "S", M: "M", L: "L",
    "均码（F）": "F",
    XS: "XS", XL: "XL", XXL: "XXL",
  },
};
```

## 🔄 处理流程

### 1. 飞书数据获取流程
```
1. 获取飞书访问令牌
2. 更新记录状态为"处理中"
3. 获取飞书记录完整数据
4. 解析字段映射
```

### 2. ERP处理流程
```
5. 实时获取最新分类配置
6. 处理订单数据（增强版）
7. 生成SKU列表（内部+供应商编码）
8. 验证/创建供应商
9. 验证/创建商品（叶子节点分类）
10. 创建采购单
```

### 3. 状态回写流程
```
11. 更新飞书记录状态
12. 回写采购单号
13. 记录处理时间
14. 记录错误信息（如有）
```

## ✅ 成功示例

### 输入数据（飞书记录）
```json
{
  "内部款式编码": "TEST001",
  "外部款式编码": "D00001", 
  "颜色": "白色",
  "S": 10,
  "M": 15,
  "L": 8,
  "采购单价": 45.50,
  "档口": "测试档口",
  "分类": "基础T恤"
}
```

### 输出结果
```json
{
  "status": "success",
  "data": {
    "purchase_order_id": "CG202507260001",
    "supplier_id": 12345,
    "processed_skus": 3,
    "processing_time": 5.685,
    "internal_style_code": "TEST001",
    "supplier_style_code": "D00001",
    "category_used": "基础T恤",
    "category_validation": {
      "valid": true,
      "message": "分类名称\"基础T恤\"有效且为叶子节点"
    }
  }
}
```

## ⚠️ 注意事项

### 分类要求
- ✅ 必须使用叶子节点分类（如"基础T恤"、"时尚T恤"）
- ❌ 不能使用父节点分类（如"T恤"、"制衣"）
- 🔍 脚本会自动验证并提供建议

### 推荐的叶子节点分类
```
服装类: 基础T恤, 时尚T恤, 衬衫, 短外套, 风衣
下装类: 休闲裤, 短裤, 牛仔裤, 打底裤
裙装类: 半身裙, 连衣裙
其他类: 背心吊带, 卫衣/绒衫, 套头衫, 马甲
```

### 错误处理
- 🔄 自动重试机制（最多3次）
- 📝 详细错误日志记录
- 🔙 失败状态自动回写到飞书

## 🚀 集成示例

### Node.js模块调用
```javascript
const erpScript = require('./test/核心脚本/聚水潭ERP集成脚本.js');

// 处理飞书数据
const result = await erpScript.processFeishuDataFlow(
  'E1Q3bDq3harjR1s8qr3cyKz6n1b',  // baseId
  'tblwJFuLDpV62Z9p',              // tableId  
  'recXXXXXXXXXXXXX'               // recordId
);

console.log('处理结果:', result);
```

### Webhook集成
```javascript
// Express.js webhook示例
app.post('/webhook/feishu', async (req, res) => {
  const { baseId, tableId, recordId } = req.body;
  
  const result = await erpScript.main('feishu', {
    baseId, tableId, recordId
  });
  
  res.json(result);
});
```

---

**✨ 现在您可以直接从飞书多维表触发聚水潭ERP采购单创建流程！**
