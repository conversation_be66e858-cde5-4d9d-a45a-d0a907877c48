/**
 * 飞书视图查看工具
 *
 * 模块名称：飞书视图查看工具
 * 模块描述：查看飞书多维表的视图结构和状态管理
 * 模块职责：视图查询、状态分析、流程管理
 * 修改时间: 2025-07-26 16:40
 */

const https = require("https");

//===================================================================================
// 📋 配置区域
//===================================================================================

const FEISHU_CONFIG = {
  APP_ID: "cli_a73da8fdc6fe900d",
  APP_SECRET: "CM0Vl3kDoKUHRim3JuJznXT1rqTBbrQa",
  BASE_URL: "https://open.feishu.cn/open-apis",
  BASE_ID: "E1Q3bDq3harjR1s8qr3cyKz6n1b",
  TABLE_ID: "tblwJFuLDpV62Z9p",
};

//===================================================================================
// 🔧 工具函数
//===================================================================================

function log(level, message) {
  const timestamp = new Date().toISOString();
  console.log(`${timestamp} [${level}] ${message}`);
}

async function makeHttpRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname + urlObj.search,
      method: options.method || "GET",
      headers: options.headers || {},
      timeout: options.timeout || 30000,
    };

    const req = https.request(requestOptions, (res) => {
      let data = "";
      res.on("data", (chunk) => { data += chunk; });
      res.on("end", () => {
        try {
          resolve(JSON.parse(data));
        } catch (error) {
          reject(new Error(`JSON解析失败: ${error.message}`));
        }
      });
    });

    req.on("error", reject);
    req.on("timeout", () => {
      req.destroy();
      reject(new Error("请求超时"));
    });

    if (options.body) {
      req.write(options.body);
    }
    req.end();
  });
}

//===================================================================================
// 🔗 飞书API函数
//===================================================================================

async function getFeishuAccessToken() {
  try {
    const url = `${FEISHU_CONFIG.BASE_URL}/auth/v3/tenant_access_token/internal`;
    const data = JSON.stringify({
      app_id: FEISHU_CONFIG.APP_ID,
      app_secret: FEISHU_CONFIG.APP_SECRET,
    });

    const response = await makeHttpRequest(url, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: data,
    });

    if (response && response.code === 0) {
      return response.tenant_access_token;
    }
    throw new Error(`获取token失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `飞书token获取失败: ${error.message}`);
    return null;
  }
}

/**
 * 获取飞书表格的视图列表
 */
async function getFeishuViews(baseId, tableId) {
  try {
    log("INFO", "开始获取飞书表格视图列表...");
    
    const token = await getFeishuAccessToken();
    if (!token) throw new Error("获取飞书token失败");

    const url = `${FEISHU_CONFIG.BASE_URL}/bitable/v1/apps/${baseId}/tables/${tableId}/views`;
    
    const response = await makeHttpRequest(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    if (response && response.code === 0) {
      return response.data.items;
    }
    throw new Error(`获取视图失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `飞书视图获取失败: ${error.message}`);
    return null;
  }
}

/**
 * 获取指定视图的记录
 */
async function getViewRecords(baseId, tableId, viewId, pageSize = 10) {
  try {
    log("INFO", `获取视图记录: ${viewId}`);
    
    const token = await getFeishuAccessToken();
    if (!token) throw new Error("获取飞书token失败");

    const url = `${FEISHU_CONFIG.BASE_URL}/bitable/v1/apps/${baseId}/tables/${tableId}/records?view_id=${viewId}&page_size=${pageSize}`;
    
    const response = await makeHttpRequest(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    if (response && response.code === 0) {
      return response.data.items;
    }
    throw new Error(`获取视图记录失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `视图记录获取失败: ${error.message}`);
    return null;
  }
}

/**
 * 分析视图和状态的关系
 */
async function analyzeViewsAndStatus() {
  try {
    log("INFO", "=== 开始分析飞书视图和状态关系 ===");
    
    // 1. 获取所有视图
    const views = await getFeishuViews(FEISHU_CONFIG.BASE_ID, FEISHU_CONFIG.TABLE_ID);
    if (!views) {
      throw new Error("无法获取视图列表");
    }
    
    log("INFO", `发现 ${views.length} 个视图:`);
    views.forEach((view, index) => {
      log("INFO", `  ${index + 1}. ${view.view_name} (${view.view_id}) - 类型: ${view.view_type}`);
    });
    
    // 2. 分析每个视图的记录
    const viewAnalysis = {};
    
    for (const view of views) {
      log("INFO", `\n--- 分析视图: ${view.view_name} ---`);
      
      const records = await getViewRecords(
        FEISHU_CONFIG.BASE_ID, 
        FEISHU_CONFIG.TABLE_ID, 
        view.view_id,
        5
      );
      
      if (!records) {
        log("WARN", `视图 ${view.view_name} 无法获取记录`);
        continue;
      }
      
      log("INFO", `视图 ${view.view_name} 包含 ${records.length} 条记录`);
      
      // 分析状态字段
      const statusAnalysis = {
        流程状态: new Set(),
        处理标签: new Set(),
        records: []
      };
      
      records.forEach((record, index) => {
        const fields = record.fields;
        const 流程状态 = fields["流程状态"];
        const 处理标签 = fields["处理标签"];
        const 内部款式编码 = fields["内部款式编码"];
        
        if (流程状态) statusAnalysis.流程状态.add(流程状态);
        if (处理标签) statusAnalysis.处理标签.add(处理标签);
        
        statusAnalysis.records.push({
          record_id: record.record_id,
          内部款式编码: 内部款式编码,
          流程状态: 流程状态,
          处理标签: 处理标签,
        });
        
        log("INFO", `  记录${index + 1}: ${内部款式编码} - 流程状态: ${流程状态}, 处理标签: ${处理标签}`);
      });
      
      viewAnalysis[view.view_name] = {
        view_id: view.view_id,
        view_type: view.view_type,
        record_count: records.length,
        流程状态_values: Array.from(statusAnalysis.流程状态),
        处理标签_values: Array.from(statusAnalysis.处理标签),
        sample_records: statusAnalysis.records,
      };
    }
    
    // 3. 输出分析结果
    console.log("\n=== 视图和状态分析结果 ===");
    console.log(JSON.stringify(viewAnalysis, null, 2));
    
    // 4. 生成状态映射建议
    log("INFO", "\n=== 状态映射建议 ===");
    
    const allFlowStatus = new Set();
    const allProcessLabels = new Set();
    
    Object.values(viewAnalysis).forEach(analysis => {
      analysis.流程状态_values.forEach(status => allFlowStatus.add(status));
      analysis.处理标签_values.forEach(label => allProcessLabels.add(label));
    });
    
    log("INFO", `发现的流程状态值: ${Array.from(allFlowStatus).join(", ")}`);
    log("INFO", `发现的处理标签值: ${Array.from(allProcessLabels).join(", ")}`);
    
    // 5. 检查视图是否基于状态过滤
    log("INFO", "\n=== 视图过滤分析 ===");
    Object.entries(viewAnalysis).forEach(([viewName, analysis]) => {
      const uniqueFlowStatus = analysis.流程状态_values;
      const uniqueProcessLabels = analysis.处理标签_values;
      
      if (uniqueFlowStatus.length === 1) {
        log("INFO", `视图 "${viewName}" 可能基于流程状态过滤: ${uniqueFlowStatus[0]}`);
      }
      
      if (uniqueProcessLabels.length === 1) {
        log("INFO", `视图 "${viewName}" 可能基于处理标签过滤: ${uniqueProcessLabels[0]}`);
      }
      
      if (uniqueFlowStatus.length > 1 && uniqueProcessLabels.length > 1) {
        log("INFO", `视图 "${viewName}" 包含多种状态，可能是综合视图`);
      }
    });
    
    return viewAnalysis;
  } catch (error) {
    log("ERROR", `视图分析失败: ${error.message}`);
    return null;
  }
}

//===================================================================================
// 🚀 主函数
//===================================================================================

async function main() {
  try {
    const analysis = await analyzeViewsAndStatus();
    
    if (analysis) {
      log("INFO", "=== 视图分析完成 ===");
      
      // 检查是否需要实现视图切换逻辑
      const viewNames = Object.keys(analysis);
      const hasMultipleViews = viewNames.length > 1;
      
      if (hasMultipleViews) {
        log("INFO", "✅ 发现多个视图，建议实现视图切换逻辑");
        log("INFO", `视图列表: ${viewNames.join(", ")}`);
      } else {
        log("INFO", "ℹ️ 只有一个视图，无需视图切换逻辑");
      }
    }
  } catch (error) {
    log("ERROR", `分析执行失败: ${error.message}`);
  }
}

// 执行分析
if (require.main === module) {
  main();
}
