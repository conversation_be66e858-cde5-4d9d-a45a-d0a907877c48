/**
 * 飞书字段解析测试工具
 *
 * 模块名称：飞书字段解析测试工具
 * 模块描述：测试飞书字段解析函数的正确性
 * 模块职责：验证各种字段类型的解析结果
 * 修改时间: 2025-07-26 16:30
 */

const https = require("https");

//===================================================================================
// 📋 配置区域
//===================================================================================

const FEISHU_CONFIG = {
  APP_ID: "cli_a73da8fdc6fe900d",
  APP_SECRET: "CM0Vl3kDoKUHRim3JuJznXT1rqTBbrQa",
  BASE_URL: "https://open.feishu.cn/open-apis",
  BASE_ID: "E1Q3bDq3harjR1s8qr3cyKz6n1b",
  TABLE_ID: "tblwJFuLDpV62Z9p",
};

//===================================================================================
// 🔧 工具函数
//===================================================================================

function log(level, message) {
  const timestamp = new Date().toISOString();
  console.log(`${timestamp} [${level}] ${message}`);
}

async function makeHttpRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname + urlObj.search,
      method: options.method || "GET",
      headers: options.headers || {},
      timeout: options.timeout || 30000,
    };

    const req = https.request(requestOptions, (res) => {
      let data = "";
      res.on("data", (chunk) => {
        data += chunk;
      });
      res.on("end", () => {
        try {
          resolve(JSON.parse(data));
        } catch (error) {
          reject(new Error(`JSON解析失败: ${error.message}`));
        }
      });
    });

    req.on("error", reject);
    req.on("timeout", () => {
      req.destroy();
      reject(new Error("请求超时"));
    });

    if (options.body) {
      req.write(options.body);
    }
    req.end();
  });
}

//===================================================================================
// 🔧 字段解析函数（从主脚本复制）
//===================================================================================

function parseFeishuFieldValue(fieldValue, fieldType = "auto") {
  if (fieldValue === null || fieldValue === undefined) {
    return "";
  }

  // 如果是简单类型，直接返回
  if (typeof fieldValue === "string" || typeof fieldValue === "number") {
    return fieldValue;
  }

  // 如果是数组类型（多选、附件等）
  if (Array.isArray(fieldValue)) {
    if (fieldValue.length === 0) return "";

    // 处理飞书附件类型（attachmentToken）
    if (fieldValue[0] && fieldValue[0].attachmentToken) {
      return fieldValue
        .map((item) => item.name || item.attachmentToken)
        .join(", ");
    }

    // 处理通用附件类型（file_token）
    if (fieldValue[0] && fieldValue[0].file_token) {
      return fieldValue.map((item) => item.name || item.file_token).join(", ");
    }

    // 处理选项类型
    if (fieldValue[0] && fieldValue[0].text) {
      return fieldValue.map((item) => item.text).join(", ");
    }

    // 处理简单数组（如档口字段）
    if (fieldValue.every((item) => typeof item === "string")) {
      return fieldValue.join(", ");
    }

    // 处理其他数组类型
    return fieldValue.join(", ");
  }

  // 如果是对象类型
  if (typeof fieldValue === "object") {
    // 处理选项类型
    if (fieldValue.text) {
      return fieldValue.text;
    }

    // 处理人员类型
    if (fieldValue.name) {
      return fieldValue.name;
    }

    // 处理日期类型
    if (fieldValue.date) {
      return fieldValue.date;
    }

    // 处理富文本类型
    if (fieldValue.content) {
      return fieldValue.content;
    }

    // 其他对象类型，尝试JSON序列化
    try {
      return JSON.stringify(fieldValue);
    } catch (error) {
      return String(fieldValue);
    }
  }

  return String(fieldValue);
}

//===================================================================================
// 🔗 飞书API函数
//===================================================================================

async function getFeishuAccessToken() {
  try {
    const url = `${FEISHU_CONFIG.BASE_URL}/auth/v3/tenant_access_token/internal`;
    const data = JSON.stringify({
      app_id: FEISHU_CONFIG.APP_ID,
      app_secret: FEISHU_CONFIG.APP_SECRET,
    });

    const response = await makeHttpRequest(url, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: data,
    });

    if (response && response.code === 0) {
      return response.tenant_access_token;
    }
    throw new Error(`获取token失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `飞书token获取失败: ${error.message}`);
    return null;
  }
}

async function getFeishuRecord(baseId, tableId, recordId) {
  try {
    const token = await getFeishuAccessToken();
    if (!token) throw new Error("获取飞书token失败");

    const url = `${FEISHU_CONFIG.BASE_URL}/bitable/v1/apps/${baseId}/tables/${tableId}/records/${recordId}`;

    const response = await makeHttpRequest(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    if (response && response.code === 0) {
      return response.data.record;
    }
    throw new Error(`获取记录失败: ${response?.msg || "未知错误"}`);
  } catch (error) {
    log("ERROR", `飞书记录获取失败: ${error.message}`);
    return null;
  }
}

//===================================================================================
// 🧪 测试函数
//===================================================================================

async function testFieldParsing() {
  try {
    log("INFO", "=== 开始飞书字段解析测试 ===");

    // 获取测试记录
    const record = await getFeishuRecord(
      FEISHU_CONFIG.BASE_ID,
      FEISHU_CONFIG.TABLE_ID,
      "recuRdw2zCUtBE"
    );

    if (!record) {
      throw new Error("无法获取测试记录");
    }

    log("INFO", "成功获取飞书记录，开始字段解析测试...");

    // 测试关键字段
    const testFields = [
      "内部款式编码",
      "外部款式编码",
      "颜色",
      "档口",
      "图片",
      "采购单",
      "S",
      "M",
      "L",
      "采购单价",
    ];

    const results = {};

    for (const fieldName of testFields) {
      const rawValue = record.fields[fieldName];
      const parsedValue = parseFeishuFieldValue(rawValue);

      results[fieldName] = {
        raw: rawValue,
        parsed: parsedValue,
        type: typeof rawValue,
        isArray: Array.isArray(rawValue),
      };

      log("INFO", `字段 "${fieldName}":`);
      log("INFO", `  原始值: ${JSON.stringify(rawValue)}`);
      log("INFO", `  解析值: ${parsedValue}`);
      log(
        "INFO",
        `  类型: ${typeof rawValue}, 是否数组: ${Array.isArray(rawValue)}`
      );
      log("INFO", "---");
    }

    // 输出最终结果
    console.log("\n=== 字段解析测试结果 ===");
    console.log(JSON.stringify(results, null, 2));

    return results;
  } catch (error) {
    log("ERROR", `字段解析测试失败: ${error.message}`);
    return null;
  }
}

//===================================================================================
// 🚀 主函数
//===================================================================================

async function main() {
  try {
    const results = await testFieldParsing();

    if (results) {
      log("INFO", "=== 字段解析测试完成 ===");

      // 检查关键字段是否正确解析
      const criticalFields = ["外部款式编码", "档口", "图片"];
      let allCorrect = true;

      for (const field of criticalFields) {
        if (
          results[field] &&
          results[field].parsed.includes("[object Object]")
        ) {
          log(
            "WARN",
            `字段 "${field}" 解析可能有问题: ${results[field].parsed}`
          );
          allCorrect = false;
        } else {
          log("INFO", `字段 "${field}" 解析正常: ${results[field].parsed}`);
        }
      }

      if (allCorrect) {
        log("INFO", "✅ 所有关键字段解析正常");
      } else {
        log("WARN", "⚠️ 部分字段解析需要改进");
      }
    }
  } catch (error) {
    log("ERROR", `测试执行失败: ${error.message}`);
  }
}

// 执行测试
if (require.main === module) {
  main();
}
