/**
 * 模块名称：飞书一键创建聚水潭采购单(WPS AirScript 一体化版本)
 * 模块描述：接收飞书webhook触发，自动创建采购单
 * 模块职责：数据处理、SKU生成、ERP API调用、状态回写
 * 修改时间: 2025-07-25 15:30
 *
 * 注意：WPS AirScript不支持模块引用，因此所有功能都在此单一文件中实现
 */

//===================================================================================
// 📋 全局日志记录系统
//===================================================================================

const GLOBAL_LOG_BUFFER = []; // 用于缓存所有日志条目
const LOG_TABLE_NAME = "脚本执行日志"; // 日志表名称
let SCRIPT_EXECUTION_START_TIME = new Date(); // 记录脚本开始执行的精确时间

//===================================================================================
// 📋 配置参数区 (合并自config.js)
//===================================================================================

// 聚水潭ERP API配置
const JUSHUITAN_CONFIG = {
  APP_KEY: "13a2701994bd4230a1ed9a12302ba30a",
  APP_SECRET: "f6e5dd9d168d4817973cb42f121218a0",
  ACCESS_TOKEN: "9a9d33072cc8450b916b7c8dd830d22c",
  BASE_URL: "https://openapi.jushuitan.com",

  // 业务默认配置
  DEFAULT_SUPPLIER_ID: 1,
  DEFAULT_WAREHOUSE_ID: 1,
  DEFAULT_WMS_CO_ID: 0,

  // API调用控制
  API_TIMEOUT: 30000,
  RETRY_TIMES: 3,
  RETRY_DELAY: 1000,
};

// 飞书API配置
const FEISHU_CONFIG = {
  APP_ID: "cli_a73da8fdc6fe900d",
  APP_SECRET: "CM0Vl3kDoKUHRim3JuJznXT1rqTBbrQa",
  BASE_URL: "https://open.feishu.cn/open-apis",

  // 多维表信息
  BASE_ID: "E1Q3bDq3harjR1s8qr3cyKz6n1b",
  TABLE_ID: "tblwJFuLDpV62Z9p",
  VIEW_ID: "vewoihqbAb",

  API_TIMEOUT: 10000,
};

// 业务配置
const BUSINESS_CONFIG = {
  SKU_PATTERN: "{style_code}-{color}-{size}",

  // 支持的尺码映射
  SIZE_MAPPING: {
    S: "S",
    M: "M",
    L: "L",
    "均码（F）": "F",
    XS: "XS",
    XL: "XL",
    XXL: "XXL",
  },

  // 飞书字段映射
  FIELD_MAPPING: {
    内部款式编码: "style_code",
    外部款式编码: "external_style_code", // 修正：对应supplier_i_id字段
    颜色: "color",
    S: "size_s_qty",
    M: "size_m_qty",
    L: "size_l_qty",
    "均码（F）": "size_f_qty",
    采购单价: "unit_price",
    采购日期: "purchase_date",
    采购人员: "purchaser",
    档口: "supplier_stall",
    图片: "product_images",
    备注: "remark",
    成分: "material",
    处理状态: "status",
    采购单号: "purchase_order_id",
    处理时间: "process_time",
    错误信息: "error_message",
  },

  // 状态枚举
  STATUS_ENUM: {
    PENDING: "待处理",
    PROCESSING: "处理中",
    COMPLETED: "已完成",
    FAILED: "失败",
  },

  // 商品默认信息
  PRODUCT_DEFAULTS: {
    BRAND: "AHMI",
    CATEGORY: "服装",
    ENABLED: 1, // 修正：数字类型，1=启用
    IS_BATCH: false,
    IS_SERIALNO: false,
  },
};

// 统一配置对象
const CONFIG = {
  JUSHUITAN: JUSHUITAN_CONFIG,
  FEISHU: FEISHU_CONFIG,
  BUSINESS: BUSINESS_CONFIG,
};

//===================================================================================
// 🛠️ 工具函数区 (合并自utils.js)
//===================================================================================

/**
 * 辅助函数：将Date对象格式化为 "YYYY-MM-DD HH:MM:SS.mmm" 字符串 (包含毫秒，用于控制台日志)
 *
 * 概述: 格式化日期对象为包含毫秒的时间字符串
 * 参数:
 *   date (Date): 要格式化的Date对象
 * 返回值:
 *   String: 格式化后的日期时间字符串
 * 修改时间: 2025-07-25 15:30
 */
function formatDateTimeWithMs(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  const milliseconds = String(date.getMilliseconds()).padStart(3, "0");
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
}

/**
 * 统一日志记录函数
 *
 * 概述: 记录带时间戳的日志信息到缓冲区和控制台
 * 参数:
 *   level (String): 日志级别 (INFO, ERROR, WARN, DEBUG)
 *   messages (Any[]): 要记录的消息内容
 * 修改时间: 2025-07-25 15:30
 */
function logAndBuffer(level, ...messages) {
  const timestamp = formatDateTimeWithMs(new Date());
  const logEntry = `${timestamp} [${level.toUpperCase()}] ${messages.join(
    " "
  )}`;
  GLOBAL_LOG_BUFFER.push(logEntry);

  switch (level.toUpperCase()) {
    case "INFO":
      console.info(logEntry);
      break;
    case "ERROR":
      console.error(logEntry);
      break;
    case "WARN":
      console.warn(logEntry);
      break;
    case "DEBUG":
      console.log(logEntry);
      break;
    default:
      console.log(logEntry);
  }
}

/**
 * 辅助函数：将Date对象格式化为 "YYYY/MM/DD HH:MM:SS" 字符串 (WPS DateTime 格式)
 *
 * 概述: 格式化Date对象为WPS表格可识别的日期时间字符串
 * 参数:
 *   date (Date): 要格式化的Date对象
 * 返回值:
 *   String: 格式化后的日期时间字符串
 * 修改时间: 2025-07-25 15:30
 */
function formatDateTimeForWpsTable(date) {
  if (!(date instanceof Date) || isNaN(date.getTime())) {
    date = new Date(); // 如果日期无效，则回退到当前时间
  }
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // 月份从0开始
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * 函数名称: writeAirScriptLogsToWpsTable
 *
 * 概述: 将AirScript的执行日志写入指定的WPS多维数据表（简化版）
 * 详细描述:
 *   1. 检查指定的日志表是否存在
 *   2. 如果日志表不存在，则创建包含两个固定字段的新表
 *   3. 如果日志表存在，则检查是否包含必需的两个字段
 *   4. 如果有必需的字段缺失，则尝试在日志表中创建这些字段
 *   5. 将传入的日志缓冲内容和脚本执行开始时间写入日志表的新记录中
 * 参数:
 *   config (Object): 日志写入配置对象
 *     - logBuffer (String[]): 包含日志条目（字符串）的数组
 *     - logTableName (String): 要写入日志的目标多维表的名称
 *     - scriptStartTime (Date): 主脚本开始执行的时间
 * 返回值:
 *   Object: 包含执行状态的对象
 *     - success (Boolean): 日志是否成功写入
 *     - logRecordId (String|null): 如果成功，则为新创建的日志记录的ID
 *     - error (String|null): 如果失败，则为错误消息
 * 修改时间: 2025-07-25 15:30
 */
function writeAirScriptLogsToWpsTable(config) {
  // 参数解构
  const { logBuffer, logTableName, scriptStartTime } = config;

  const result = { success: false, logRecordId: null, error: null };

  if (
    typeof Application === "undefined" ||
    typeof Application.Sheet === "undefined"
  ) {
    result.error = "Application 或 Application.Sheet 未定义。可能非WPS环境。";
    console.error("[writeAirScriptLogsToWpsTable] " + result.error);
    if (logBuffer && logBuffer.length > 0) {
      console.error(
        "[writeAirScriptLogsToWpsTable] 缓存的日志:\n" + logBuffer.join("\n")
      );
    }
    return result;
  }

  if (!logBuffer || logBuffer.length === 0) {
    result.success = true; // 认为操作成功，因为没有日志需要写
    return result;
  }

  const logContentForTable = logBuffer.join("\n");

  // 固定的字段定义
  const FIXED_LOG_FIELDS = [
    { name: "执行时间", type: "MultiLineText" },
    { name: "日志内容", type: "MultiLineText" },
  ];

  let logSheetId = null;
  try {
    const sheets = Application.Sheet.GetSheets();
    let existingLogSheet = null;
    if (Array.isArray(sheets)) {
      for (let i = 0; i < sheets.length; i++) {
        if (sheets[i] && String(sheets[i].name) === String(logTableName)) {
          existingLogSheet = sheets[i];
          break;
        }
      }
    }

    if (existingLogSheet) {
      logSheetId = Number(existingLogSheet.id);

      const existingFieldsResult = Application.Field.GetFields({
        SheetId: logSheetId,
      });
      const existingFieldNames = [];
      if (Array.isArray(existingFieldsResult)) {
        for (let i = 0; i < existingFieldsResult.length; i++) {
          if (existingFieldsResult[i] && existingFieldsResult[i].name != null) {
            existingFieldNames.push(String(existingFieldsResult[i].name));
          }
        }
      }

      const fieldsToAdd = [];
      for (let i = 0; i < FIXED_LOG_FIELDS.length; i++) {
        const requiredField = FIXED_LOG_FIELDS[i];
        if (
          !existingFieldNames.some(
            (name) => String(name) === String(requiredField.name)
          )
        ) {
          fieldsToAdd.push({
            name: String(requiredField.name),
            type: String(requiredField.type),
          });
        }
      }

      if (fieldsToAdd.length > 0) {
        try {
          const createFieldsResult = Application.Field.CreateFields({
            SheetId: logSheetId,
            Fields: fieldsToAdd,
          });
          if (
            !createFieldsResult ||
            !Array.isArray(createFieldsResult) ||
            createFieldsResult.length !== fieldsToAdd.length ||
            createFieldsResult.some((f) => !f || typeof f.id === "undefined")
          ) {
            console.error(
              `[错误][writeAirScriptLogsToWpsTable] 未能向 '${logTableName}' 添加部分/全部缺失字段. API响应: ${JSON.stringify(
                createFieldsResult
              )}`
            );
          }
        } catch (fieldCreationError) {
          console.error(
            `[错误][writeAirScriptLogsToWpsTable] 在为 '${logTableName}' 执行 Application.Field.CreateFields 时发生错误: ${
              fieldCreationError.message || JSON.stringify(fieldCreationError)
            }`
          );
        }
      }
    } else {
      try {
        const newSheet = Application.Sheet.CreateSheet({
          Name: String(logTableName),
          Views: [{ name: "所有日志", type: "Grid" }],
          Fields: FIXED_LOG_FIELDS,
        });
        if (newSheet && typeof newSheet.id !== "undefined") {
          logSheetId = Number(newSheet.id);
        } else {
          result.error = `创建日志表 '${logTableName}' 失败. API响应: ${JSON.stringify(
            newSheet
          )}`;
          console.error("[错误][writeAirScriptLogsToWpsTable] " + result.error);
          return result;
        }
      } catch (sheetCreationError) {
        result.error = `在为 '${logTableName}' 执行 Application.Sheet.CreateSheet 时发生错误: ${
          sheetCreationError.message || JSON.stringify(sheetCreationError)
        }`;
        console.error("[错误][writeAirScriptLogsToWpsTable] " + result.error);
        return result;
      }
    }

    if (logSheetId !== null) {
      const executionTimeFormatted = formatDateTimeForWpsTable(scriptStartTime);
      const recordDataFields = {
        执行时间: executionTimeFormatted,
        日志内容: logContentForTable,
      };

      try {
        const createRecordParams = {
          SheetId: logSheetId,
          Records: [{ fields: recordDataFields }],
        };
        const createResult =
          Application.Record.CreateRecords(createRecordParams);

        if (
          createResult &&
          Array.isArray(createResult) &&
          createResult.length > 0 &&
          typeof createResult[0].id !== "undefined"
        ) {
          result.success = true;
          result.logRecordId = createResult[0].id;
        } else {
          result.error = `未能将日志写入表 '${logTableName}'. API响应: ${JSON.stringify(
            createResult
          )}`;
          console.error(
            "[错误][writeAirScriptLogsToWpsTable] " +
              result.error +
              " 数据: " +
              JSON.stringify(recordDataFields)
          );
        }
      } catch (recordCreationError) {
        result.error = `在为 '${logTableName}' 执行 Application.Record.CreateRecords 时发生错误: ${
          recordCreationError.message || JSON.stringify(recordCreationError)
        }`;
        console.error(
          "[错误][writeAirScriptLogsToWpsTable] " +
            result.error +
            " 数据: " +
            JSON.stringify(recordDataFields)
        );
      }
    } else {
      result.error = "日志表的logSheetId为空，无法写入日志。";
      console.error("[错误][writeAirScriptLogsToWpsTable] " + result.error);
    }
  } catch (e) {
    result.error = `在 writeAirScriptLogsToWpsTable 中发生意外错误: ${
      e.message || JSON.stringify(e)
    }`;
    console.error("[错误][writeAirScriptLogsToWpsTable] " + result.error);
    if (e.stack)
      console.error(
        "[错误][writeAirScriptLogsToWpsTable] 错误堆栈: " + e.stack
      );
  }
  return result;
}

/**
 * 函数名称：安全解析JSON
 *
 * 概述: 安全地解析JSON字符串，避免异常
 * 参数:
 *   jsonString (String): JSON字符串
 *   defaultValue (Any): 解析失败时的默认值
 * 返回值:
 *   Any: 解析结果或默认值
 * 修改时间: 2025-07-25 15:30
 */
function safeJSONParse(jsonString, defaultValue = null) {
  try {
    if (typeof jsonString === "string") {
      return JSON.parse(jsonString);
    }
    return jsonString;
  } catch (error) {
    logAndBuffer("ERROR", `JSON解析失败: ${error.message}`);
    return defaultValue;
  }
}

/**
 * 函数名称：清理字符串
 *
 * 概述: 清理字符串中的空白字符和特殊字符
 * 参数:
 *   str (String): 待处理字符串
 * 返回值:
 *   String: 清理后的字符串
 * 修改时间: 2025-07-25 15:30
 */
function cleanString(str) {
  if (!str || typeof str !== "string") {
    return "";
  }
  return str.trim().replace(/\s+/g, " ");
}

/**
 * 函数名称：验证是否为有效数字
 *
 * 概述: 检查值是否为有效的正数
 * 参数:
 *   value (Any): 待验证的值
 * 返回值:
 *   Boolean: 是否为有效正数
 * 修改时间: 2025-07-25 15:30
 */
function isValidNumber(value) {
  const num = parseFloat(value);
  return !isNaN(num) && num > 0;
}

/**
 * 函数名称：格式化日期时间
 *
 * 概述: 将日期对象格式化为指定格式的字符串
 * 参数:
 *   date (Date): 日期对象
 *   format (String): 格式化模板，默认YYYY-MM-DD HH:mm:ss
 * 返回值:
 *   String: 格式化后的日期字符串
 * 修改时间: 2025-07-25 15:30
 */
function formatDateTime(date = new Date(), format = "YYYY-MM-DD HH:mm:ss") {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");

  return format
    .replace("YYYY", year)
    .replace("MM", month)
    .replace("DD", day)
    .replace("HH", hours)
    .replace("mm", minutes)
    .replace("ss", seconds);
}

/**
 * 函数名称：获取时间戳
 *
 * 概述: 获取当前时间的时间戳字符串，用于API调用
 * 返回值:
 *   String: ISO格式的时间戳
 * 修改时间: 2025-07-25 15:30
 */
function getTimestamp() {
  return new Date().toISOString();
}

/**
 * 函数名称：验证必需字段
 *
 * 概述: 验证对象是否包含所有必需字段
 * 参数:
 *   data (Object): 待验证的数据对象
 *   requiredFields (Array): 必需字段列表
 * 返回值:
 *   Object: 验证结果 {valid: boolean, missing: Array}
 * 修改时间: 2025-07-25 15:30
 */
function validateRequiredFields(data, requiredFields) {
  const missing = [];

  for (let field of requiredFields) {
    if (
      !data.hasOwnProperty(field) ||
      data[field] === null ||
      data[field] === undefined ||
      (typeof data[field] === "string" && data[field].trim() === "")
    ) {
      missing.push(field);
    }
  }

  return {
    valid: missing.length === 0,
    missing: missing,
  };
}

/**
 * 函数名称：验证SKU格式
 *
 * 概述: 验证SKU是否符合标准格式
 * 参数:
 *   sku (String): SKU字符串
 * 返回值:
 *   Boolean: 是否符合格式
 * 修改时间: 2025-07-25 15:30
 */
function validateSKUFormat(sku) {
  if (!sku || typeof sku !== "string") {
    return false;
  }

  // SKU格式: 款式编码-颜色-尺码
  const parts = sku.split("-");
  return parts.length >= 3 && parts.every((part) => part.trim().length > 0);
}

//===================================================================================
// 🔧 聚水潭API调用区 (基于sku_update_other_1_by_purchase.py)
//===================================================================================

/**
 * 函数名称：计算MD5哈希值
 *
 * 概述: 在WPS环境下计算MD5哈希
 * 参数:
 *   str (String): 待计算的字符串
 * 返回值:
 *   String: MD5哈希值
 * 修改时间: 2025-07-25 15:30
 */
function calculateMD5(str) {
  // WPS AirScript环境下的MD5实现
  // 注意：实际使用时需要根据WPS提供的加密API进行调整
  return Crypto.MD5(str).toString();
}

/**
 * 函数名称：构建聚水潭API参数 (基于sku_update_other_1_by_purchase.py)
 *
 * 概述: 构建API签名参数，按ASCII码升序排列
 * 参数:
 *   bizParams (Object): 业务参数
 * 返回值:
 *   Object: 完整的API参数对象
 * 修改时间: 2025-07-25 15:30
 */
function buildJushuitanParams(bizParams) {
  const params = {
    access_token: CONFIG.JUSHUITAN.ACCESS_TOKEN,
    app_key: CONFIG.JUSHUITAN.APP_KEY,
    charset: "utf-8",
    timestamp: Math.floor(Date.now() / 1000),
    version: "2",
    biz: JSON.stringify(bizParams),
  };

  // 按ASCII码排序
  const sortedParams = {};
  Object.keys(params)
    .sort()
    .forEach((key) => {
      sortedParams[key] = params[key];
    });

  return sortedParams;
}

/**
 * 函数名称：生成聚水潭API签名 (基于sku_update_other_1_by_purchase.py)
 *
 * 概述: 生成聚水潭API调用所需的签名
 * 参数:
 *   params (Object): API参数对象
 * 返回值:
 *   String: 生成的签名字符串
 * 修改时间: 2025-07-25 15:30
 */
function generateJushuitanSignature(params) {
  try {
    // 排除sign参数，按ASCII码升序拼接
    const filteredParams = Object.keys(params)
      .filter((key) => key !== "sign")
      .sort()
      .map((key) => key + params[key])
      .join("");

    const signString = CONFIG.JUSHUITAN.APP_SECRET + filteredParams;

    // 计算MD5
    return calculateMD5(signString);
  } catch (error) {
    logAndBuffer("ERROR", `签名生成失败: ${error.message}`);
    return null;
  }
}

/**
 * 函数名称：调用聚水潭API (基于sku_update_other_1_by_purchase.py的重试机制)
 *
 * 概述: 带重试机制的聚水潭API调用方法
 * 参数:
 *   apiUrl (String): 完整的API URL
 *   bizParams (Object): 业务参数
 *   apiName (String): API名称(用于日志)
 * 返回值:
 *   Object: API响应结果的data部分，失败返回null
 * 修改时间: 2025-07-25 15:30
 */
function callJushuitanAPI(apiUrl, bizParams, apiName) {
  const maxRetries = 5;

  try {
    logAndBuffer("INFO", `调用聚水潭API: ${apiName}`);

    // 构建参数
    const params = buildJushuitanParams(bizParams);
    const sign = generateJushuitanSignature(params);
    if (!sign) {
      throw new Error("签名生成失败");
    }
    params.sign = sign;

    // 构建请求体
    const formData = [];
    for (let key in params) {
      formData.push(
        `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`
      );
    }
    const body = formData.join("&");

    // 重试机制
    for (let retryCount = 0; retryCount < maxRetries; retryCount++) {
      try {
        logAndBuffer(
          "INFO",
          `正在请求 ${apiName} (第${retryCount + 1}次尝试)...`
        );

        const response = HTTP.post(apiUrl, body, {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
          timeout: 20000,
        });

        if (response.status === 200) {
          const result = JSON.parse(response.body);

          // 检查API返回码 (基于Python版本的错误处理)
          if (result.code === 0) {
            // 成功
            logAndBuffer("INFO", `${apiName} 请求成功。`);
            return result.data; // 返回data部分
          } else if (result.code === 199 || result.code === 200) {
            // 频率限制
            logAndBuffer(
              "WARN",
              `${apiName} 频率限制: ${result.msg}，等待重试...（第${
                retryCount + 1
              }次）`
            );
            continue;
          } else {
            // 其他API错误码
            logAndBuffer(
              "WARN",
              `${apiName} 错误响应: ${JSON.stringify(result)}，等待重试...（第${
                retryCount + 1
              }次）`
            );
            continue;
          }
        } else {
          throw new Error(`HTTP请求失败: ${response.status}`);
        }
      } catch (httpError) {
        logAndBuffer(
          "WARN",
          `${apiName} 请求异常: ${httpError.message}，等待重试...（第${
            retryCount + 1
          }次）`
        );
        if (retryCount === maxRetries - 1) {
          throw httpError;
        }
        continue;
      }
    }

    logAndBuffer(
      "WARN",
      `${apiName} 重试 ${maxRetries} 次失败，跳过本次请求。`
    );
    return null;
  } catch (error) {
    logAndBuffer("ERROR", `聚水潭API调用失败 [${apiName}]: ${error.message}`);
    return null;
  }
}

//===================================================================================
// 🔗 飞书API调用区
//===================================================================================

/**
 * 函数名称：获取飞书访问令牌
 *
 * 概述: 获取飞书API访问令牌
 * 返回值:
 *   String: 访问令牌
 * 修改时间: 2025-07-25 15:30
 */
function getFeishuAccessToken() {
  try {
    const response = HTTP.post(
      `${CONFIG.FEISHU.BASE_URL}/auth/v3/tenant_access_token/internal`,
      JSON.stringify({
        app_id: CONFIG.FEISHU.APP_ID,
        app_secret: CONFIG.FEISHU.APP_SECRET,
      }),
      {
        headers: { "Content-Type": "application/json" },
        timeout: 10000,
      }
    );

    if (response.status === 200) {
      const result = JSON.parse(response.body);
      if (result.code === 0) {
        return result.tenant_access_token;
      }
    }

    throw new Error(`获取token失败: ${response.status}`);
  } catch (error) {
    logAndBuffer("ERROR", `飞书token获取失败: ${error.message}`);
    return null;
  }
}

/**
 * 函数名称：更新飞书记录状态
 *
 * 概述: 更新飞书表格记录的状态信息
 * 参数:
 *   baseId (String): 多维表BaseID
 *   tableId (String): 表格ID
 *   recordId (String): 记录ID
 *   statusData (Object): 状态数据
 * 返回值:
 *   Boolean: 更新是否成功
 * 修改时间: 2025-07-25 15:30
 */
function updateFeishuRecord(baseId, tableId, recordId, statusData) {
  try {
    const token = getFeishuAccessToken();
    if (!token) {
      throw new Error("获取飞书token失败");
    }

    const updateData = {
      fields: {
        处理状态: statusData.status || "",
        采购单号: statusData.purchase_order_id || "",
        处理时间: new Date().toISOString(),
        错误信息: statusData.error || "",
      },
    };

    const url = `${CONFIG.FEISHU.BASE_URL}/bitable/v1/apps/${baseId}/tables/${tableId}/records/${recordId}`;

    const response = HTTP.put(url, JSON.stringify(updateData), {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      timeout: 10000,
    });

    if (response.status === 200) {
      logAndBuffer("INFO", "飞书记录状态更新成功");
      return true;
    } else {
      throw new Error(`更新失败: ${response.status}`);
    }
  } catch (error) {
    logAndBuffer("ERROR", `飞书记录更新失败: ${error.message}`);
    return false;
  }
}

//===================================================================================
// 💼 核心业务逻辑区
//===================================================================================

/**
 * 函数名称：处理订单数据
 *
 * 概述: 解析和验证从飞书传入的订单数据
 * 参数:
 *   rawData (Object): 原始数据
 * 返回值:
 *   Object: 处理后的订单数据
 * 修改时间: 2025-07-25 15:30
 */
function processOrderData(rawData) {
  try {
    logAndBuffer("INFO", "开始处理订单数据...");

    const data = typeof rawData === "string" ? JSON.parse(rawData) : rawData;

    // 提取核心字段
    const styleCode = String(data["内部款式编码"] || "").trim();
    const externalStyleCode = String(data["外部款式编码"] || "").trim(); // 添加外部款式编码
    const color = String(data["颜色"] || "").trim();
    const unitPrice = String(data["采购单价"] || "0").trim();
    const supplier = String(data["档口"] || "").trim();

    // 提取图片URL
    let imageUrl = "";
    const images = data["图片"];
    if (Array.isArray(images) && images.length > 0) {
      // 飞书返回的图片对象是一个数组，每个对象里有不同字段，我们需要找到URL
      const imageObject = images[0];
      // 根据飞书实际返回的附件格式，它通常是 { file_token: "...", url: "..." }
      // 我们需要 'url' 字段
      if (imageObject && typeof imageObject === "object" && imageObject.url) {
        imageUrl = imageObject.url;
        logAndBuffer("INFO", `成功提取图片URL: ${imageUrl}`);
      } else {
        logAndBuffer("WARN", "图片字段格式不符合预期，未找到URL。");
      }
    }

    // 提取尺码数量
    const sizes = {};
    const sizeFields = [
      { field: "S", code: "S" },
      { field: "M", code: "M" },
      { field: "L", code: "L" },
      { field: "均码（F）", code: "F" },
    ];

    for (let sizeField of sizeFields) {
      const qty = data[sizeField.field];
      if (qty && String(qty).trim() && String(qty).trim() !== "0") {
        try {
          const quantity = parseInt(String(qty).trim());
          if (quantity > 0) {
            sizes[sizeField.code] = quantity;
          }
        } catch (e) {
          logAndBuffer("WARN", `尺码${sizeField.field}数量解析失败: ${qty}`);
        }
      }
    }

    // 数据验证
    if (!styleCode) {
      throw new Error("缺少内部款式编码");
    }
    if (!color) {
      throw new Error("缺少颜色信息");
    }
    if (Object.keys(sizes).length === 0) {
      throw new Error("缺少有效的尺码数量");
    }

    // 记录外部款式编码信息 (非必填，但如果有的话会用作supplier_i_id)
    if (externalStyleCode) {
      logAndBuffer("INFO", `检测到外部款式编码: ${externalStyleCode}`);
    } else {
      logAndBuffer(
        "WARN",
        "未检测到外部款式编码，将使用内部款式编码作为供应商款式编码"
      );
    }

    const processedData = {
      styleCode: styleCode,
      externalStyleCode: externalStyleCode, // 添加外部款式编码
      color: color,
      unitPrice: parseFloat(unitPrice) || 0,
      supplier: supplier,
      sizes: sizes,
      imageUrl: imageUrl,
    };

    logAndBuffer(
      "INFO",
      `订单数据处理成功: ${styleCode}-${color}, ${
        Object.keys(sizes).length
      }个尺码`
    );
    return processedData;
  } catch (error) {
    logAndBuffer("ERROR", `订单数据处理失败: ${error.message}`);
    return null;
  }
}

/**
 * 函数名称：生成SKU列表
 *
 * 概述: 根据订单数据生成标准SKU列表
 * 参数:
 *   orderData (Object): 处理后的订单数据
 * 返回值:
 *   Array: SKU列表
 * 修改时间: 2025-07-25 15:30
 */
function generateSKUList(orderData) {
  try {
    logAndBuffer("INFO", "开始生成SKU列表...");

    const skuList = [];
    const { styleCode, externalStyleCode, color, unitPrice, sizes } = orderData;

    for (let size in sizes) {
      const quantity = sizes[size];
      const sku = `${styleCode}-${color}-${size}`;

      skuList.push({
        sku: sku,
        styleCode: styleCode,
        externalStyleCode: externalStyleCode, // 添加外部款式编码
        color: color,
        size: size,
        quantity: quantity,
        unitPrice: unitPrice,
      });
    }

    logAndBuffer("INFO", `SKU列表生成成功: ${skuList.length}个SKU`);
    return skuList;
  } catch (error) {
    logAndBuffer("ERROR", `SKU生成失败: ${error.message}`);
    return null;
  }
}

/**
 * 函数名称：生成外部单号
 *
 * 概述: 生成唯一的外部采购单号
 * 返回值:
 *   String: 外部单号
 * 修改时间: 2025-07-25 15:30
 */
function generateExternalId() {
  const now = new Date();
  const timestamp = now.getTime().toString();
  const randomSuffix = Math.floor(Math.random() * 1000)
    .toString()
    .padStart(3, "0");
  return `WPS_${timestamp}_${randomSuffix}`;
}

/**
 * 函数名称：验证和创建商品
 *
 * 概述: 在聚水潭中验证商品存在性，不存在则创建
 * 参数:
 *   skuList (Array): SKU列表
 *   imageUrl (String): 商品图片URL
 * 返回值:
 *   Array: 验证通过的SKU列表
 * 修改时间: 2025-07-25 15:30
 */
function verifyAndCreateProducts(skuList, imageUrl) {
  try {
    logAndBuffer("INFO", "开始验证和创建商品...");

    const verifiedSkus = [];

    for (let i = 0; i < skuList.length; i++) {
      const skuInfo = skuList[i];
      const sku = skuInfo.sku;

      logAndBuffer("INFO", `验证商品 ${i + 1}/${skuList.length}: ${sku}`);

      // 查询商品是否存在 - 修正API参数
      const queryUrl = `${CONFIG.JUSHUITAN.BASE_URL}/open/sku/query`;
      const queryParams = {
        sku_ids: sku, // 修正：使用正确的参数名
        page_index: 1,
        page_size: 1,
      };

      const queryResult = callJushuitanAPI(
        queryUrl,
        queryParams,
        `商品查询-${sku}`
      );

      let exists = false;
      if (queryResult && queryResult.datas) {
        const goods = queryResult.datas || [];
        exists = goods.length > 0;
      }

      if (!exists) {
        logAndBuffer("INFO", `商品不存在，开始创建: ${sku}`);

        // 使用正确的商品创建API参数
        const createUrl = `${CONFIG.JUSHUITAN.BASE_URL}/open/jushuitan/itemsku/upload`;

        // 修正：构建符合API规范的参数
        const createParams = {
          items: [
            {
              sku_id: sku, // 商品编码 (必填)
              i_id: skuInfo.styleCode, // 款式编码 (必填)
              name: `${skuInfo.styleCode} ${skuInfo.color} ${skuInfo.size}码`, // 商品名称 (必填)
              brand: CONFIG.BUSINESS.PRODUCT_DEFAULTS.BRAND, // 品牌
              c_name: CONFIG.BUSINESS.PRODUCT_DEFAULTS.CATEGORY, // 分类
              pic: imageUrl || "", // 商品图片URL
              enabled: CONFIG.BUSINESS.PRODUCT_DEFAULTS.ENABLED, // 修正：数字类型
              batch_enabled: CONFIG.BUSINESS.PRODUCT_DEFAULTS.IS_BATCH, // 修正：正确字段名
              is_series_number: CONFIG.BUSINESS.PRODUCT_DEFAULTS.IS_SERIALNO, // 是否启用序列号
              properties_value: `${skuInfo.color};${skuInfo.size}`, // 颜色及规格
              s_price: skuInfo.unitPrice || 0, // 基本售价
              supplier_i_id: skuInfo.externalStyleCode || skuInfo.styleCode, // 供应商款式编码 (外部款式编码)
            },
          ],
        };

        const createResult = callJushuitanAPI(
          createUrl,
          createParams,
          `商品创建-${sku}`
        );

        if (!createResult || !createResult.datas) {
          logAndBuffer("ERROR", `商品创建失败: ${sku}`);
          continue;
        }

        // 检查创建结果
        const createData = createResult.datas[0];
        if (createData && createData.is_success) {
          logAndBuffer("INFO", `商品创建成功: ${sku}`);
        } else {
          logAndBuffer(
            "ERROR",
            `商品创建失败: ${sku}, 消息: ${createData?.msg || "未知错误"}`
          );
          continue;
        }
      } else {
        logAndBuffer("INFO", `商品已存在: ${sku}`);
      }

      verifiedSkus.push(skuInfo);
    }

    logAndBuffer(
      "INFO",
      `商品验证完成: ${verifiedSkus.length}/${skuList.length}个商品可用`
    );
    return verifiedSkus;
  } catch (error) {
    logAndBuffer("ERROR", `商品验证失败: ${error.message}`);
    return null;
  }
}

/**
 * 函数名称：创建采购单
 *
 * 概述: 在聚水潭中创建采购单
 * 参数:
 *   skuList (Array): 验证通过的SKU列表
 *   orderData (Object): 订单数据
 *   supplierId (Number): 供应商ID
 * 返回值:
 *   String: 采购单ID
 * 修改时间: 2025-07-25 15:30
 */
function createPurchaseOrder(skuList, orderData, supplierId) {
  try {
    logAndBuffer("INFO", "开始创建采购单...");

    // 构建采购单商品列表 - 修正参数结构
    const items = [];
    for (let skuInfo of skuList) {
      items.push({
        sku_id: skuInfo.sku, // 修正：使用sku_id而非sku
        qty: skuInfo.quantity,
        price: skuInfo.unitPrice,
      });
    }

    // 生成外部单号
    const externalId = generateExternalId();

    // 修正：使用正确的API endpoint和参数
    const createUrl = `${CONFIG.JUSHUITAN.BASE_URL}/open/jushuitan/purchase/upload`;
    const createParams = {
      supplier_id: supplierId, // 供应商编码 (必填)
      external_id: externalId, // 外部单号 (必填)
      wms_co_id: CONFIG.JUSHUITAN.DEFAULT_WMS_CO_ID, // 修正：使用wms_co_id
      items: items, // 商品列表 (必填)
      remark: `WPS自动创建 - ${orderData.styleCode} ${orderData.color}`, // 备注
      is_confirm: true, // 是否自动确认单据
    };

    const result = callJushuitanAPI(createUrl, createParams, "采购单创建");

    // 修正：根据API文档调整返回值处理
    if (result && result.data && result.data.po_id) {
      const purchaseOrderId = result.data.po_id;
      logAndBuffer("INFO", `采购单创建成功: ${purchaseOrderId}`);
      return purchaseOrderId;
    } else {
      throw new Error(`采购单创建失败: ${JSON.stringify(result)}`);
    }
  } catch (error) {
    logAndBuffer("ERROR", `采购单创建失败: ${error.message}`);
    return null;
  }
}

/**
 * 函数名称：验证和创建供应商
 *
 * 概述: 在聚水潭中验证供应商存在性，不存在则创建
 * 参数:
 *   supplierName (String): 供应商名称
 * 返回值:
 *   Number: 供应商ID
 * 修改时间: 2025-07-25 15:30
 */
function verifyAndCreateSupplier(supplierName) {
  try {
    logAndBuffer("INFO", `开始验证和创建供应商: ${supplierName}`);

    // 1. 查询供应商是否存在
    const queryUrl = `${CONFIG.JUSHUITAN.BASE_URL}/open/supplier/query`;
    const queryParams = {
      names: [supplierName],
      page_index: 1,
      page_size: 1,
    };

    const queryResult = callJushuitanAPI(
      queryUrl,
      queryParams,
      `供应商查询-${supplierName}`
    );

    if (queryResult && queryResult.datas && queryResult.datas.length > 0) {
      const supplierId = queryResult.datas[0].supplier_id;
      logAndBuffer("INFO", `供应商已存在: ${supplierName}, ID: ${supplierId}`);
      return supplierId;
    }

    // 2. 如果不存在，则创建供应商 - 修正参数结构
    logAndBuffer("INFO", `供应商不存在，开始创建: ${supplierName}`);
    const createUrl = `${CONFIG.JUSHUITAN.BASE_URL}/open/supplier/upload`;

    // 修正：直接传递数组，并添加supplier_code字段
    const createParams = [
      {
        name: supplierName, // 供应商名称 (必填)
        supplier_code: `SUPPLIER_${supplierName}_${Date.now()}`, // 修正：添加供应商编码 (必填)
        enabled: true, // 是否生效 (必填)
      },
    ];

    const createResult = callJushuitanAPI(
      createUrl,
      createParams,
      `供应商创建-${supplierName}`
    );

    if (
      createResult &&
      createResult.datas &&
      createResult.datas.length > 0 &&
      createResult.datas[0].issuccess
    ) {
      const newSupplierId = createResult.datas[0].id;
      logAndBuffer(
        "INFO",
        `供应商创建成功: ${supplierName}, ID: ${newSupplierId}`
      );
      return newSupplierId;
    } else {
      throw new Error(`供应商创建失败: ${JSON.stringify(createResult)}`);
    }
  } catch (error) {
    logAndBuffer("ERROR", `供应商验证或创建失败: ${error.message}`);
    throw error; // 向上抛出异常，由主函数捕获
  }
}

//===================================================================================
// 🚀 主函数区
//===================================================================================

/**
 * 函数名称：主处理函数
 *
 * 概述: 接收飞书webhook触发，执行完整的采购单创建流程
 * 修改时间: 2025-07-25 15:30
 */
function createPurchaseOrderMain() {
  const startTime = Date.now();
  logAndBuffer("INFO", "=== 开始执行采购单创建流程 ===");

  try {
    // 1. 获取输入参数
    const argv = Context.argv || {};
    const action = argv.action;
    const recordId = argv.record_id;
    const tableId = argv.table_id;
    const baseId = argv.base_id;
    const rawData = argv.data;

    logAndBuffer("INFO", `接收参数: action=${action}, recordId=${recordId}`);

    if (action !== "create_purchase_order") {
      throw new Error(`不支持的操作: ${action}`);
    }

    if (!recordId || !tableId || !baseId || !rawData) {
      throw new Error("缺少必要参数");
    }

    // 2. 先更新状态为处理中
    updateFeishuRecord(baseId, tableId, recordId, {
      status: CONFIG.BUSINESS.STATUS_ENUM.PROCESSING,
    });

    // 3. 处理订单数据
    const orderData = processOrderData(rawData);
    if (!orderData) {
      throw new Error("订单数据处理失败");
    }

    // 4. 验证和创建供应商
    if (!orderData.supplier) {
      throw new Error("缺少供应商（档口）信息");
    }
    const supplierId = verifyAndCreateSupplier(orderData.supplier);
    if (!supplierId) {
      throw new Error("获取供应商ID失败");
    }

    // 5. 生成SKU列表
    const skuList = generateSKUList(orderData);
    if (!skuList || skuList.length === 0) {
      throw new Error("SKU生成失败");
    }

    // 6. 验证和创建商品
    const verifiedSkus = verifyAndCreateProducts(skuList, orderData.imageUrl);
    if (!verifiedSkus || verifiedSkus.length === 0) {
      throw new Error("商品验证失败");
    }

    // 7. 创建采购单
    const purchaseOrderId = createPurchaseOrder(
      verifiedSkus,
      orderData,
      supplierId
    );
    if (!purchaseOrderId) {
      throw new Error("采购单创建失败");
    }

    // 8. 更新成功状态
    updateFeishuRecord(baseId, tableId, recordId, {
      status: CONFIG.BUSINESS.STATUS_ENUM.COMPLETED,
      purchase_order_id: purchaseOrderId,
    });

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    logAndBuffer("INFO", `=== 采购单创建完成 ===`);
    logAndBuffer("INFO", `处理时间: ${duration}秒`);
    logAndBuffer("INFO", `采购单号: ${purchaseOrderId}`);
    logAndBuffer("INFO", `处理SKU: ${verifiedSkus.length}个`);

    // 返回成功结果
    return {
      status: "success",
      data: {
        purchase_order_id: purchaseOrderId,
        processed_skus: verifiedSkus.length,
        processing_time: duration,
      },
    };
  } catch (error) {
    logAndBuffer("ERROR", `=== 采购单创建失败 ===`);
    logAndBuffer("ERROR", `错误信息: ${error.message}`);

    // 更新失败状态
    if (Context.argv?.record_id) {
      updateFeishuRecord(
        Context.argv.base_id,
        Context.argv.table_id,
        Context.argv.record_id,
        {
          status: CONFIG.BUSINESS.STATUS_ENUM.FAILED,
          error: error.message,
        }
      );
    }

    // 返回失败结果
    return {
      status: "error",
      message: error.message,
    };
  }
}

//===================================================================================
// 🎯 WPS AirScript 入口函数
//===================================================================================

/**
 * WPS AirScript 主函数入口
 * 这是WPS AirScript环境调用的入口点
 *
 * 概述: 包装主函数，添加统一日志记录功能
 * 修改时间: 2025-07-25 15:30
 */
function main() {
  // 使用IIFE包装主函数，确保日志正确写入
  return (function MainExecutionWrapper() {
    SCRIPT_EXECUTION_START_TIME = new Date(); // 确保在开始时重置

    try {
      logAndBuffer("INFO", "脚本开始执行主要逻辑...");

      // 执行核心业务逻辑
      const result = createPurchaseOrderMain();

      logAndBuffer("INFO", "主逻辑执行完毕。");
      return result;
    } catch (e) {
      logAndBuffer(
        "ERROR",
        "脚本顶层执行发生致命错误: " + (e.message || JSON.stringify(e))
      );
      if (e.stack) logAndBuffer("ERROR", "顶层错误堆栈: " + e.stack);

      return {
        status: "error",
        message: e.message || "未知错误",
      };
    } finally {
      // 调用通用日志函数 - 写入WPS表格
      const loggingConfig = {
        logBuffer: GLOBAL_LOG_BUFFER,
        logTableName: LOG_TABLE_NAME,
        scriptStartTime: SCRIPT_EXECUTION_START_TIME,
      };

      const loggingOutcome = writeAirScriptLogsToWpsTable(loggingConfig);

      if (!loggingOutcome.success) {
        console.error(
          "!! 严重错误: 脚本完成时未能将脚本执行日志写入WPS表: " +
            loggingOutcome.error
        );
      } else {
        console.log(
          "脚本执行日志已成功写入WPS表格，记录ID: " + loggingOutcome.logRecordId
        );
      }
    }
  })();
}
