/**
 * 聚水潭商品分类格式诊断工具
 *
 * 功能：
 * 1. 查询现有商品的分类格式
 * 2. 验证分类字段的正确使用方式
 * 3. 提供分类格式修复建议
 * 4. 生成正确的商品上传参数
 *
 * 修改时间: 2025-01-27
 */

//===================================================================================
// 📋 配置参数区
//===================================================================================

// 聚水潭ERP API配置
const JUSHUITAN_CONFIG = {
  APP_KEY: "13a2701994bd4230a1ed9a12302ba30a",
  APP_SECRET: "f6e5dd9d168d4817973cb42f121218a0",
  ACCESS_TOKEN: "9a9d33072cc8450b916b7c8dd830d22c",
  BASE_URL: "https://openapi.jushuitan.com",

  // API调用控制
  API_TIMEOUT: 30000,
  RETRY_TIMES: 3,
  RETRY_DELAY: 1000,
};

//===================================================================================
// 🛠️ 工具函数区
//===================================================================================

/**
 * 简单的控制台日志函数
 */
function log(level, ...messages) {
  const timestamp = new Date().toISOString();
  const logMessage = `${timestamp} [${level.toUpperCase()}] ${messages.join(
    " "
  )}`;

  switch (level.toUpperCase()) {
    case "INFO":
      console.info(logMessage);
      break;
    case "ERROR":
      console.error(logMessage);
      break;
    case "WARN":
      console.warn(logMessage);
      break;
    case "DEBUG":
      console.log(logMessage);
      break;
    default:
      console.log(logMessage);
  }
}

/**
 * 计算MD5哈希值
 */
async function calculateMD5(str) {
  if (typeof require !== "undefined") {
    const crypto = require("crypto");
    return crypto.createHash("md5").update(str).digest("hex");
  }
  throw new Error("浏览器环境暂不支持MD5计算");
}

/**
 * 延迟函数
 */
function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

//===================================================================================
// 🔧 API调用基础函数
//===================================================================================

/**
 * 构建聚水潭API参数
 */
function buildJushuitanParams(bizParams) {
  const params = {
    access_token: JUSHUITAN_CONFIG.ACCESS_TOKEN,
    app_key: JUSHUITAN_CONFIG.APP_KEY,
    charset: "utf-8",
    timestamp: Math.floor(Date.now() / 1000),
    version: "2",
    biz: JSON.stringify(bizParams),
  };

  // 按ASCII码排序
  const sortedParams = {};
  const keys = Object.keys(params).sort();
  keys.forEach((key) => {
    sortedParams[key] = params[key];
  });

  return sortedParams;
}

/**
 * 生成聚水潭API签名
 */
async function generateJushuitanSignature(params) {
  try {
    const filteredKeys = Object.keys(params)
      .filter((key) => key !== "sign")
      .sort();

    const signParts = filteredKeys.map((key) => key + params[key]);
    const signString = JUSHUITAN_CONFIG.APP_SECRET + signParts.join("");

    return await calculateMD5(signString);
  } catch (error) {
    log("ERROR", `签名生成失败: ${error.message}`);
    return null;
  }
}

/**
 * 调用聚水潭API的通用函数
 */
async function callJushuitanAPI(apiPath, bizParams, apiName) {
  const maxRetries = JUSHUITAN_CONFIG.RETRY_TIMES;
  const apiUrl = `${JUSHUITAN_CONFIG.BASE_URL}${apiPath}`;

  try {
    log("INFO", `调用聚水潭API: ${apiName}`);
    log("DEBUG", `API路径: ${apiPath}`);

    // 构建参数和签名
    const params = buildJushuitanParams(bizParams);
    const sign = await generateJushuitanSignature(params);
    if (!sign) {
      throw new Error("签名生成失败");
    }
    params.sign = sign;

    // 构建请求体
    const formData = new URLSearchParams();
    Object.keys(params).forEach((key) => {
      formData.append(key, params[key]);
    });

    // 重试机制
    for (let retryCount = 0; retryCount < maxRetries; retryCount++) {
      try {
        log("INFO", `正在请求 ${apiName} (第${retryCount + 1}次尝试)...`);

        const controller = new AbortController();
        const timeoutId = setTimeout(
          () => controller.abort(),
          JUSHUITAN_CONFIG.API_TIMEOUT
        );

        const response = await fetch(apiUrl, {
          method: "POST",
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
          body: formData,
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          const responseText = await response.text();
          log("DEBUG", `响应内容: ${responseText}`);

          const result = JSON.parse(responseText);

          if (result.code === 0) {
            log("INFO", `${apiName} 请求成功`);
            return result.data;
          } else if (result.code === 199 || result.code === 200) {
            log(
              "WARN",
              `${apiName} 频率限制: ${result.msg}，等待重试...（第${
                retryCount + 1
              }次）`
            );
            await delay(JUSHUITAN_CONFIG.RETRY_DELAY * (retryCount + 1));
            continue;
          } else {
            log(
              "WARN",
              `${apiName} 错误响应: ${JSON.stringify(result)}，等待重试...（第${
                retryCount + 1
              }次）`
            );
            await delay(JUSHUITAN_CONFIG.RETRY_DELAY);
            continue;
          }
        } else {
          throw new Error(`HTTP请求失败: ${response.status}`);
        }
      } catch (httpError) {
        log(
          "WARN",
          `${apiName} 请求异常: ${httpError.message}，等待重试...（第${
            retryCount + 1
          }次）`
        );
        if (retryCount === maxRetries - 1) {
          throw httpError;
        }
        await delay(JUSHUITAN_CONFIG.RETRY_DELAY * (retryCount + 1));
        continue;
      }
    }

    log("ERROR", `${apiName} 重试 ${maxRetries} 次失败`);
    return null;
  } catch (error) {
    log("ERROR", `聚水潭API调用失败 [${apiName}]: ${error.message}`);
    return null;
  }
}

//===================================================================================
// 🔍 分类诊断功能
//===================================================================================

/**
 * 查询现有商品的分类格式
 */
async function queryExistingProductCategories() {
  try {
    log("INFO", "开始查询现有商品的分类格式...");

    // 查询最近一段时间的商品数据
    const endTime = new Date();
    const startTime = new Date(endTime.getTime() - 7 * 24 * 60 * 60 * 1000); // 最近7天

    const queryParams = {
      modified_begin: startTime.toISOString().slice(0, 19).replace("T", " "),
      modified_end: endTime.toISOString().slice(0, 19).replace("T", " "),
      page_index: 1,
      page_size: 50,
    };

    const result = await callJushuitanAPI(
      "/open/sku/query",
      queryParams,
      "查询现有商品分类格式"
    );

    if (!result || !result.datas || result.datas.length === 0) {
      log("WARN", "没有查询到现有商品数据，尝试查询更大范围...");

      // 尝试查询所有启用的商品（不指定时间范围，但可能会有限制）
      const allProductsParams = {
        page_index: 1,
        page_size: 20,
      };

      const allResult = await callJushuitanAPI(
        "/open/sku/query",
        allProductsParams,
        "查询所有商品分类格式"
      );

      if (!allResult || !allResult.datas || allResult.datas.length === 0) {
        log("ERROR", "无法获取任何现有商品数据");
        return null;
      }

      result = allResult;
    }

    log("INFO", `成功查询到 ${result.datas.length} 个商品`);

    // 分析分类字段的使用情况
    const categoryAnalysis = {
      samples: [],
      categoryFields: new Set(),
      categoryValues: new Set(),
      cIdValues: new Set(),
    };

    result.datas.forEach((product, index) => {
      const sample = {
        sku_id: product.sku_id,
        name: product.name,
        category: product.category,
        c_id: product.c_id,
        vc_name: product.vc_name,
        brand: product.brand,
      };

      categoryAnalysis.samples.push(sample);

      // 收集分类字段信息
      if (product.category) {
        categoryAnalysis.categoryFields.add("category");
        categoryAnalysis.categoryValues.add(product.category);
      }
      if (product.c_id) {
        categoryAnalysis.cIdValues.add(product.c_id);
      }

      // 只显示前10个样本
      if (index < 10) {
        log("DEBUG", `样本商品 ${index + 1}:`);
        log("DEBUG", `  SKU: ${product.sku_id}`);
        log("DEBUG", `  名称: ${product.name}`);
        log("DEBUG", `  分类(category): "${product.category || ""}"`);
        log("DEBUG", `  分类ID(c_id): ${product.c_id || ""}`);
        log("DEBUG", `  虚拟分类(vc_name): "${product.vc_name || ""}"`);
        log("DEBUG", `  品牌: "${product.brand || ""}"`);
      }
    });

    log("INFO", "=== 分类字段分析结果 ===");
    log("INFO", `发现的分类名称数量: ${categoryAnalysis.categoryValues.size}`);
    log("INFO", `发现的分类ID数量: ${categoryAnalysis.cIdValues.size}`);

    if (categoryAnalysis.categoryValues.size > 0) {
      log("INFO", "现有商品使用的分类名称:");
      Array.from(categoryAnalysis.categoryValues).forEach((cat) => {
        log("INFO", `  - "${cat}"`);
      });
    }

    return categoryAnalysis;
  } catch (error) {
    log("ERROR", `查询现有商品分类失败: ${error.message}`);
    return null;
  }
}

/**
 * 加载本地分类配置
 */
async function loadLocalCategoryConfig() {
  try {
    if (typeof require !== "undefined") {
      const fs = require("fs");
      const path = require("path");

      const configPath = path.join(
        __dirname,
        "../配置数据/聚水潭官方分类配置.json"
      );

      if (fs.existsSync(configPath)) {
        const configContent = fs.readFileSync(configPath, "utf8");
        const config = JSON.parse(configContent);
        log(
          "INFO",
          `成功加载本地分类配置: ${config.category_names_count}个分类`
        );
        return config;
      } else {
        log("WARN", `本地分类配置文件不存在: ${configPath}`);
        return null;
      }
    } else {
      log("WARN", "浏览器环境无法读取本地文件");
      return null;
    }
  } catch (error) {
    log("ERROR", `加载本地分类配置失败: ${error.message}`);
    return null;
  }
}

/**
 * 分析分类配置问题
 */
async function analyzeCategoryIssues(existingCategories, localConfig) {
  log("INFO", "=== 开始分析分类配置问题 ===");

  const issues = [];
  const recommendations = [];

  if (!existingCategories || existingCategories.samples.length === 0) {
    issues.push("无法获取现有商品的分类信息");
    recommendations.push(
      "建议先手动在ERP中创建一些测试商品，以便了解正确的分类格式"
    );
  }

  if (!localConfig) {
    issues.push("无法加载本地分类配置");
    recommendations.push("建议重新运行分类获取工具来更新本地配置");
  }

  // 检查"制衣"分类问题
  const targetCategory = "制衣";
  let categoryExists = false;
  let categoryId = null;

  if (localConfig && localConfig.category_names.includes(targetCategory)) {
    categoryExists = true;
    // 查找制衣分类的ID
    const categoryItem = localConfig.full_categories.find(
      (cat) => cat.name === targetCategory
    );
    if (categoryItem) {
      categoryId = categoryItem.c_id;
    }
    log("INFO", `"${targetCategory}"分类在本地配置中存在`);
    log("INFO", `分类ID: ${categoryId}`);
  } else {
    issues.push(`"${targetCategory}"分类在本地配置中不存在`);
  }

  if (
    existingCategories &&
    existingCategories.categoryValues.has(targetCategory)
  ) {
    log("INFO", `"${targetCategory}"分类在现有商品中被使用`);
  } else if (existingCategories) {
    issues.push(`"${targetCategory}"分类在现有商品中未被使用`);

    // 寻找相似的分类
    const similarCategories = Array.from(
      existingCategories.categoryValues
    ).filter(
      (cat) => cat.includes("制") || cat.includes("衣") || cat.includes("服装")
    );

    if (similarCategories.length > 0) {
      log("INFO", `发现相似分类: ${similarCategories.join(", ")}`);
      recommendations.push(`考虑使用相似分类: ${similarCategories.join(", ")}`);
    }
  }

  // 分析分类字段使用方式
  if (existingCategories && existingCategories.samples.length > 0) {
    const categorySample = existingCategories.samples.find((s) => s.category);
    if (categorySample) {
      log("INFO", "=== 正确的分类字段使用示例 ===");
      log("INFO", `商品: ${categorySample.name}`);
      log("INFO", `分类字段(category): "${categorySample.category}"`);
      log("INFO", `分类ID(c_id): ${categorySample.c_id}`);

      recommendations.push(
        `使用 c_name 字段设置分类，值应为: "${categorySample.category}"`
      );
    }
  }

  // 输出问题和建议
  if (issues.length > 0) {
    log("WARN", "=== 发现的问题 ===");
    issues.forEach((issue, index) => {
      log("WARN", `${index + 1}. ${issue}`);
    });
  }

  if (recommendations.length > 0) {
    log("INFO", "=== 修复建议 ===");
    recommendations.forEach((rec, index) => {
      log("INFO", `${index + 1}. ${rec}`);
    });
  }

  return {
    issues,
    recommendations,
    targetCategoryExists: categoryExists,
    targetCategoryId: categoryId,
    existingCategoryFormats: existingCategories
      ? Array.from(existingCategories.categoryValues)
      : [],
  };
}

/**
 * 生成修复后的商品上传参数
 */
function generateFixedProductParams(analysis, localConfig) {
  log("INFO", "=== 生成修复后的商品上传参数 ===");

  const recommendations = [];

  // 基于分析结果生成建议的分类配置
  let recommendedCategory = "";

  if (
    analysis.targetCategoryExists &&
    analysis.existingCategoryFormats.includes("制衣")
  ) {
    recommendedCategory = "制衣";
    recommendations.push(`使用"制衣"分类（已验证存在且被使用）`);
  } else if (analysis.existingCategoryFormats.length > 0) {
    // 寻找服装相关的分类
    const clothingCategories = analysis.existingCategoryFormats.filter(
      (cat) =>
        cat.includes("服装") || cat.includes("上衣") || cat.includes("衣")
    );

    if (clothingCategories.length > 0) {
      recommendedCategory = clothingCategories[0];
      recommendations.push(
        `建议使用"${recommendedCategory}"分类（现有商品中使用的服装相关分类）`
      );
    } else {
      recommendedCategory = analysis.existingCategoryFormats[0];
      recommendations.push(
        `暂时使用"${recommendedCategory}"分类（现有商品中使用的分类）`
      );
    }
  } else if (localConfig && localConfig.category_names.includes("制衣")) {
    recommendedCategory = "制衣";
    recommendations.push(`使用"制衣"分类（本地配置中存在，但需要验证）`);
  } else {
    recommendedCategory = "";
    recommendations.push(`使用空分类（避免分类验证问题）`);
  }

  // 生成修复后的商品参数示例
  const fixedParams = {
    sku_id: "TEST001-白色-S",
    i_id: "TEST001",
    name: "测试商品 白色 S码",
    brand: "AHMI",
    c_name: recommendedCategory, // 使用分析后的推荐分类
    pic: "",
    enabled: 1,
    properties_value: "白色;S",
    s_price: 89.9,
    supplier_i_id: "D00001", // 供应商款式编码
    supplier_sku_id: "D00001-白色-S", // 供应商商品编码
  };

  log("INFO", "修复后的商品上传参数示例:");
  log("INFO", JSON.stringify(fixedParams, null, 2));

  recommendations.forEach((rec, index) => {
    log("INFO", `修复建议 ${index + 1}: ${rec}`);
  });

  return {
    fixedParams,
    recommendedCategory,
    recommendations,
  };
}

/**
 * 测试分类上传
 */
async function testCategoryUpload(fixedParams) {
  log("INFO", "=== 测试分类上传 ===");

  try {
    const testParams = {
      items: [fixedParams],
    };

    log("INFO", "测试上传参数:");
    log("INFO", JSON.stringify(testParams, null, 2));

    // 注意：这里只是模拟测试，实际测试时需要谨慎
    log("WARN", "注意：这是模拟测试，不会实际上传商品");
    log("INFO", "如需实际测试，请取消注释以下代码:");
    log(
      "INFO",
      "const result = await callJushuitanAPI('/open/jushuitan/itemsku/upload', testParams, '测试分类上传');"
    );

    /*
    const result = await callJushuitanAPI(
      "/open/jushuitan/itemsku/upload",
      testParams,
      "测试分类上传"
    );

    if (result) {
      log("INFO", "测试上传成功:");
      log("INFO", JSON.stringify(result, null, 2));
      return true;
    } else {
      log("ERROR", "测试上传失败");
      return false;
    }
    */

    return true;
  } catch (error) {
    log("ERROR", `测试上传失败: ${error.message}`);
    return false;
  }
}

//===================================================================================
// 🚀 主流程
//===================================================================================

/**
 * 完整的分类诊断流程
 */
async function runCategoryDiagnosis() {
  const startTime = Date.now();
  log("INFO", "=== 开始聚水潭商品分类格式诊断 ===");

  try {
    // 步骤1: 查询现有商品的分类格式
    log("INFO", "步骤1: 查询现有商品的分类格式");
    const existingCategories = await queryExistingProductCategories();

    // 步骤2: 加载本地分类配置
    log("INFO", "步骤2: 加载本地分类配置");
    const localConfig = await loadLocalCategoryConfig();

    // 步骤3: 分析分类配置问题
    log("INFO", "步骤3: 分析分类配置问题");
    const analysis = await analyzeCategoryIssues(
      existingCategories,
      localConfig
    );

    // 步骤4: 生成修复建议
    log("INFO", "步骤4: 生成修复建议");
    const fixResult = generateFixedProductParams(analysis, localConfig);

    // 步骤5: 模拟测试上传
    log("INFO", "步骤5: 模拟测试上传");
    const testSuccess = await testCategoryUpload(fixResult.fixedParams);

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    log("INFO", "=== 诊断完成 ===");
    log("INFO", `诊断耗时: ${duration}秒`);

    return {
      status: "success",
      data: {
        existingCategories: existingCategories,
        localConfig: localConfig
          ? {
              total_count: localConfig.total_count,
              category_names_count: localConfig.category_names_count,
              update_time: localConfig.update_time,
            }
          : null,
        analysis: analysis,
        fixResult: fixResult,
        testSuccess: testSuccess,
        processingTime: duration,
      },
    };
  } catch (error) {
    log("ERROR", "=== 诊断失败 ===");
    log("ERROR", `错误信息: ${error.message}`);

    return {
      status: "error",
      message: error.message,
    };
  }
}

//===================================================================================
// 🎯 主入口函数
//===================================================================================

/**
 * 主函数入口
 */
async function main() {
  try {
    log("INFO", "开始执行聚水潭商品分类格式诊断工具...");

    const result = await runCategoryDiagnosis();

    log("INFO", "诊断工具执行完毕。");
    return result;
  } catch (error) {
    log("ERROR", `执行发生致命错误: ${error.message}`);
    return {
      status: "error",
      message: error.message,
    };
  }
}

//===================================================================================
// 🔧 模块导出和直接执行支持
//===================================================================================

// 如果在Node.js环境中直接执行此文件
if (typeof require !== "undefined" && require.main === module) {
  main()
    .then((result) => {
      console.log("=== 最终诊断结果 ===");
      console.log(JSON.stringify(result, null, 2));
      process.exit(result && result.status === "success" ? 0 : 1);
    })
    .catch((error) => {
      console.error("执行错误:", error);
      process.exit(1);
    });
}

// 模块导出
if (typeof module !== "undefined" && module.exports) {
  module.exports = {
    main,
    runCategoryDiagnosis,
    queryExistingProductCategories,
    loadLocalCategoryConfig,
    analyzeCategoryIssues,
    generateFixedProductParams,
    testCategoryUpload,
    callJushuitanAPI,
    JUSHUITAN_CONFIG,
  };
}

// 浏览器环境全局导出
if (typeof window !== "undefined") {
  window.JushuitanCategoryDiagnosticTool = {
    main,
    runCategoryDiagnosis,
    queryExistingProductCategories,
    analyzeCategoryIssues,
    generateFixedProductParams,
    callJushuitanAPI,
    JUSHUITAN_CONFIG,
  };
}
