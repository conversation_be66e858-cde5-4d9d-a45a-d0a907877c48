# 🎨 WPS AirScript 开发规范

适用于 [飞书一键创建聚水潭采购单(WPS AirScript 一体化版本).js](mdc:wps_airscript/飞书一键创建聚水潭采购单(WPS AirScript 一体化版本).js)

## 📚 相关技术文档引用

### 🔧 核心开发指南
- **参数处理问题** → 查看 [WPS_AirScript_脚本参数使用指南.md](mdc:docs/WPS_AirScript_脚本参数使用指南.md)
  - Context.argv 参数获取方式
  - 返回值捕获机制 (必须在顶层作用域return)
  - 脚本入参JSON格式规范

- **API调用问题** → 查看 [WPS_多维表格_AirScript脚本使用指南.md](mdc:docs/WPS_多维表格_AirScript脚本使用指南.md)
  - Application.Sheet/Field/Record API使用
  - 参数类型严格性要求 (SheetId必须是数字类型)
  - 内置对象和函数限制

- **日志记录问题** → 查看 [WPS_AirScript_通用日志记录规范.md](mdc:docs/WPS_AirScript_通用日志记录规范.md)
  - writeAirScriptLogsToWpsTable 标准实现
  - 日志表自动创建机制
  - 时间格式化规范

- **鉴权配置问题** → 查看 [WPS_AirScript_API_Token_说明.md](mdc:docs/WPS_AirScript_API_Token_说明.md)
  - API Token 配置方法
  - 业务流程鉴权步骤
  - 外部服务访问权限

### 🔗 集成方案文档
- **整体方案架构** → 查看 [飞书Webhook触发WPS_AirScript一键创建采购单方案.md](mdc:docs/飞书Webhook触发WPS_AirScript一键创建采购单方案.md)
- **聚水潭API详情** → 查看 [聚水潭API集成细节说明.md](mdc:docs/聚水潭API集成细节说明.md)

## 📋 必须遵循的代码结构

### 1. 文件组织规范
```javascript
/**
 * 模块名称：[简洁的模块名称]
 * 模块描述：[1-2句话功能概述]
 * 模块职责：[核心职责与功能边界]
 * 修改时间: YYYY-MM-DD HH:MM
 */

//===================================================================================
// 📋 全局日志记录系统
//===================================================================================
const GLOBAL_LOG_BUFFER = []; // 用于缓存所有日志条目
const LOG_TABLE_NAME = "脚本执行日志"; // 日志表名称
let SCRIPT_EXECUTION_START_TIME = new Date(); // 记录脚本开始执行的精确时间

//===================================================================================
// 📋 配置参数区
//===================================================================================

//===================================================================================
// 🛠️ 工具函数区  
//===================================================================================

//===================================================================================
// 💼 核心业务逻辑区
//===================================================================================

//===================================================================================
// 🚀 主函数区
//===================================================================================
```

### 2. 配置管理标准
- **必须使用统一的CONFIG对象**:
```javascript
const CONFIG = {
  JUSHUITAN: { /* 聚水潭配置 */ },
  FEISHU: { /* 飞书配置 */ },
  BUSINESS: { /* 业务规则配置 */ }
};
```

### 3. 参数获取规范 (基于脚本参数使用指南)
```javascript
// 第一层：WPS AirScript参数获取
const argv = Context.argv || {};
const action = argv.action;
const recordId = argv.record_id;
const rawData = argv.data;

// 第二层：业务数据解析  
function processOrderData(rawData) {
  const data = typeof rawData === "string" ? JSON.parse(rawData) : rawData;
  // 解析飞书字段...
}
```

### 4. 函数注释规范
每个函数都必须包含：
```javascript
/**
 * 函数名称：[动词短语描述功能]
 *
 * 概述: [一句话核心功能]
 * 调用的函数: [此方法调用的其他函数（需包含文件目录）]
 * 参数:
 *   param1 (类型): 描述
 * 返回值:
 *   类型: 描述
 * 修改时间: YYYY-MM-DD HH:MM
 */
```

### 5. 日志记录标准 (基于通用日志记录规范)
- **必须使用统一的日志函数**: `logAndBuffer(level, message)`
- **必须在finally块中写入日志**: 调用`writeAirScriptLogsToWpsTable()`
- **日志级别**: INFO, ERROR, WARN, DEBUG
- **时间格式**: 控制台用毫秒精度，表格用 "YYYY/MM/DD HH:MM:SS"

### 6. 错误处理标准
- **所有API调用必须有错误处理**
- **重试机制使用标准模式** (最多5次重试)
- **状态更新**: 及时更新飞书记录状态 (处理中/已完成/失败)

### 7. 返回值规范 (基于脚本参数使用指南)
```javascript
// ⚠️ 关键：return 必须在顶层作用域，不能在IIFE中
function main() {
  try {
    // 业务逻辑...
    return { status: "success", data: result };
  } catch (error) {
    return { status: "error", message: error.message };
  } finally {
    // 日志写入
    writeAirScriptLogsToWpsTable(loggingConfig);
  }
}
```

### 8. SKU生成规范
- **格式**: `{内部款式编码}-{颜色}-{尺码}`
- **示例**: `AMX0063-白色-M`
- **验证**: 使用`validateSKUFormat(sku)`函数

### 9. API调用规范 (基于多维表格脚本使用指南)
- **类型严格性**: SheetId 必须是数字类型 `Number(sheetId)`
- **字符串转换**: API返回值需显式转换 `String(field.name)`
- **聚水潭API**: 使用`callJushuitanAPI(apiUrl, bizParams, apiName)`
- **飞书API**: 使用`updateFeishuRecord(baseId, tableId, recordId, statusData)`

### 10. 状态管理
```javascript
const STATUS_ENUM = {
    PENDING: "待处理",
    PROCESSING: "处理中", 
    COMPLETED: "已完成",
    FAILED: "失败"
};
```

## 🔧 开发注意事项

1. **WPS环境限制**: 不支持模块引用，所有功能必须在单一文件中实现
2. **HTTP调用**: 使用WPS提供的HTTP对象进行API调用  
3. **日志记录**: 所有日志必须使用中文，便于排查问题
4. **超时设置**: API调用超时时间不超过30秒
5. **MD5计算**: 使用`Crypto.MD5()`进行签名计算
6. **参数验证**: 入参和API参数都需要严格类型检查
7. **错误恢复**: 提供友好的错误信息和状态回写机制

## 🚨 常见问题与解决方案

### 参数问题
- **Context.argv 获取不到参数** → 检查调用方式和JSON格式
- **返回值无法捕获** → 确保return在顶层作用域，不在IIFE中

### API调用问题  
- **SheetId类型错误** → 使用 `Number(sheetId)` 转换
- **字段值操作异常** → 使用 `String(field.name)` 显式转换
- **HTTP超时** → 检查网络和超时设置

### 日志问题
- **日志未写入表格** → 检查表格权限和字段创建
- **时间格式错误** → 使用标准时间格式化函数

### 聚水潭API问题
- **签名错误** → 检查参数排序和MD5计算
- **API限频** → 实现重试机制和延迟策略
2. **HTTP调用**: 使用WPS提供的HTTP对象进行API调用  
3. **日志记录**: 所有日志必须使用中文，便于排查问题
4. **超时设置**: API调用超时时间不超过30秒
5. **MD5计算**: 使用`Crypto.MD5()`进行签名计算